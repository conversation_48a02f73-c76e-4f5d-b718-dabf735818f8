package com.jxntv.gvideo.search.client;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexDTO;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexSearchDTO;
import com.jxntv.gvideo.search.client.fallback.MediaResourceIndexSearchClientFallback;

@FeignClient(name = "search-service", contextId = "media-resource-index",fallbackFactory = MediaResourceIndexSearchClientFallback.class)
public interface MediaResourceIndexSearchClient {
    @PostMapping("/api/media/resource/index/search")
    Result<PageDTO<MediaResourceIndexDTO>> search(@RequestBody MediaResourceIndexSearchDTO searchDTO);
}