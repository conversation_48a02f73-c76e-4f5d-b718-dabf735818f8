package com.jxntv.gvideo.media.client.dto.search;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class MediaResourceSearchDTO extends PageSearchDTO {
    private List<Integer> contentTypes;
    private List<Integer> excludeContentTypes;
    private List<Integer> playStyles;
    private List<Integer> status;
    private String showName;
    private String internalName;
    private List<Long> labelIds;
    private Integer releaseType;
    private List<Long> releaseIds;
    private List<Long> mainDataIds;
    private List<Long> columnIds;
    private Long materialId;
    private Long resourceId;
    private Long fileId;
    private Integer dateType;
    private Boolean isSearch;
    private Date start;
    private Date end;
    private List<Long> tenantIds;
    private List<Long> updateUserIds;
    private Long id;
    private Long groupId;
    private Long topicId;
    private Long startId;
    private Long endId;
    /**
     * 问答类型，0-非问答，1-提问，2-回答，3-爆料
     */
    private Integer dataType;
    /**
     * 问答类型，0-非问答，1-提问，2-回答，3-爆料
     */
    private Integer filterDataType;

    /**
     * 平台类型：0-今视频、2-小程序
     */
    private Integer platform;

    /**
     * 是否匿名发帖
     */
    private Boolean isAnonymous;
}
