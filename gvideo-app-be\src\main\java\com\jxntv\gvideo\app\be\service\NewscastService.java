package com.jxntv.gvideo.app.be.service;

import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastEntireVO;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastSplitPage;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastSplitVO;
import com.jxntv.gvideo.media.client.dto.newscast.*;

import java.util.List;

public interface NewscastService {
    NewscastEntireVO getEntire(String publishDate);

    Page<NewscastEntireVO> entireList(Integer pageNum, Integer pageSize);

    NewscastSplitPage<NewscastSplitVO> splitList(String startDate, Integer pageNum, Integer pageSize);


    JxntvCategoryResponse h5CategoryList(JxntvCategroyRequest param);

    JxntvContentResponse h5ContentList(JxntvContentRequest param);

    JxntvMediaResponse getMediaIds(List<Long> contentIds);

    JxntvCategoryResponse h5CategoryListV2(JxntvCategroyRequest param);

    JxntvContentResponse h5ContentListV2(JxntvContentRequest param);

}
