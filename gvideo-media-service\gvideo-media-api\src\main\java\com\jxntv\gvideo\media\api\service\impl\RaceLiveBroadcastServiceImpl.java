package com.jxntv.gvideo.media.api.service.impl;

import java.time.LocalDateTime;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.media.api.converter.RaceConverter;
import com.jxntv.gvideo.media.api.domain.entity.RaceLiveBroadcast;
import com.jxntv.gvideo.media.api.repository.RaceLiveBroadcastMapper;
import com.jxntv.gvideo.media.api.service.RaceLiveBroadcastService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastSearchDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 比赛直播服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RaceLiveBroadcastServiceImpl extends ServiceImpl<RaceLiveBroadcastMapper, RaceLiveBroadcast> implements RaceLiveBroadcastService {

    @Resource
    private RaceConverter raceConverter;

    @Override
    public Boolean addLiveBroadcast(RaceLiveBroadcastDTO dto) {
        if (Objects.isNull(dto)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛直播信息不能为空");
        }

        RaceLiveBroadcast entity = raceConverter.convertToEntity(dto);
        entity.setCreateDate(LocalDateTime.now());
        entity.setUpdateDate(LocalDateTime.now());
        entity.setDelFlag(0);
        return this.save(entity);
    }

    @Override
    public Boolean updateLiveBroadcast(RaceLiveBroadcastDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛直播ID不能为空");
        }

        RaceLiveBroadcast entity = this.getById(dto.getId());
        if (entity == null || Objects.equals(1, entity.getDelFlag())) {
            throw new CodeMessageException(CodeMessage.NOT_FOUND.getCode(), "比赛直播信息不存在");
        }

        entity = raceConverter.convertToEntity(dto);
        entity.setUpdateDate(LocalDateTime.now());
        return this.updateById(entity);
    }

    @Override
    public PageDTO<RaceLiveBroadcastDTO> page(RaceLiveBroadcastSearchDTO searchDTO) {
        LambdaQueryWrapper<RaceLiveBroadcast> queryWrapper = new LambdaQueryWrapper<>();
        if (!ObjectUtils.isEmpty(searchDTO.getId())) {
            queryWrapper.eq(RaceLiveBroadcast::getId, searchDTO.getId());
        }
        if (!ObjectUtils.isEmpty(searchDTO.getActivityId())) {
            queryWrapper.eq(RaceLiveBroadcast::getActivityId, searchDTO.getActivityId());
        }
        if (!ObjectUtils.isEmpty(searchDTO.getResourceId())) {
            queryWrapper.eq(RaceLiveBroadcast::getResourceId, searchDTO.getResourceId());
        }
        if (StringUtils.hasText(searchDTO.getTitle())) {
            queryWrapper.like(RaceLiveBroadcast::getTitle, searchDTO.getTitle());
        }
        if (!ObjectUtils.isEmpty(searchDTO.getDelFlag())) {
            queryWrapper.eq(RaceLiveBroadcast::getDelFlag, searchDTO.getDelFlag());
        }
        queryWrapper.eq(RaceLiveBroadcast::getDelFlag, 0);
        queryWrapper.orderByDesc(RaceLiveBroadcast::getCreateDate);

        IPage<RaceLiveBroadcast> page = this.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), queryWrapper);
        return PageUtils.pageOf(page, raceConverter::convertToDto);
    }

    @Override
    public RaceLiveBroadcastDTO getLiveBroadcastById(Long id) {
        if (Objects.isNull(id)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛直播ID不能为空");
        }
        RaceLiveBroadcast entity = this.getById(id);
        if (entity == null || Objects.equals(1, entity.getDelFlag())) {
            throw new CodeMessageException(CodeMessage.NOT_FOUND.getCode(), "比赛直播信息不存在");
        }
        return raceConverter.convertToDto(entity);
    }

    @Override
    public Boolean deleteLiveBroadcast(Long id) {
        if (Objects.isNull(id)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛直播ID不能为空");
        }
        RaceLiveBroadcast entity = this.getById(id);
        if (entity == null || Objects.equals(1, entity.getDelFlag())) {
            throw new CodeMessageException(CodeMessage.NOT_FOUND.getCode(), "比赛直播信息不存在");
        }
        entity.setDelFlag(1);
        entity.setUpdateDate(LocalDateTime.now());
        return this.updateById(entity);
    }

    @Override
    public RaceLiveBroadcastDTO getLatestLiveBroadcastByActivityId(Long activityId) {
        if (Objects.isNull(activityId)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "活动ID不能为空");
        }

        // 构建查询条件：指定活动ID、未删除、当前时间在开始和结束时间之间
        LambdaQueryWrapper<RaceLiveBroadcast> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RaceLiveBroadcast::getActivityId, activityId);
        queryWrapper.eq(RaceLiveBroadcast::getDelFlag, 0);
        queryWrapper.le(RaceLiveBroadcast::getStartTime, LocalDateTime.now());
        queryWrapper.ge(RaceLiveBroadcast::getEndTime, LocalDateTime.now());

        // 按ID倒序排列，确保获取ID最大的记录
        queryWrapper.orderByDesc(RaceLiveBroadcast::getId);

        queryWrapper.last(" limit 1 ");

        // 获取第一条记录（即ID最大的符合条件记录）
        RaceLiveBroadcast entity = this.getOne(queryWrapper);
        if (entity == null) {
            return null;
        }

        return raceConverter.convertToDto(entity);
    }
}
