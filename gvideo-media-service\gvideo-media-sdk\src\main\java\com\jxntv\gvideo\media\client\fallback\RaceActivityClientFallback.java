package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.RaceActivityClient;
import com.jxntv.gvideo.media.client.dto.race.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 球赛活动客户端降级处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RaceActivityClientFallback implements FallbackFactory<RaceActivityClient> {

    @Override
    public RaceActivityClient create(Throwable throwable) {
        return new RaceActivityClient() {
            @Override
            public Result<PageDTO<RaceActivityDTO>> page(RaceActivitySearchDTO searchDTO) {
                log.error("RaceActivityClient.page() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> addActivity(RaceActivityDTO dto) {
                log.error("RaceActivityClient.addActivity() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateActivity(RaceActivityDTO dto) {
                log.error("RaceActivityClient.updateActivity() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<RaceActivityDTO> getActivityById(Long id) {
                log.error("RaceActivityClient.getActivityById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> addTeam(RaceTeamDTO dto) {
                log.error("RaceActivityClient.addTeam() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateTeam(RaceTeamDTO dto) {
                log.error("RaceActivityClient.updateTeam() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<RaceTeamDTO>> teamPage(RaceTeamSearchDTO searchDTO) {
                log.error("RaceActivityClient.teamPage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<RaceTeamDTO> getTeamById(Long id) {
                log.error("RaceActivityClient.getTeamById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> addSchedule(RaceScheduleDTO dto) {
                log.error("RaceActivityClient.addSchedule() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateSchedule(RaceScheduleDTO dto) {
                log.error("RaceActivityClient.updateSchedule() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<RaceScheduleDTO>> schedulePage(RaceScheduleSearchDTO searchDTO) {
                log.error("RaceActivityClient.schedulePage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<RaceScheduleDTO> getScheduleById(Long id) {
                log.error("RaceActivityClient.getScheduleById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteSchedule(Long id) {
                log.error("RaceActivityClient.deleteSchedule() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> addLiveBroadcast(RaceLiveBroadcastDTO dto) {
                log.error("RaceActivityClient.addLiveBroadcast() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateLiveBroadcast(RaceLiveBroadcastDTO dto) {
                log.error("RaceActivityClient.updateLiveBroadcast() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<RaceLiveBroadcastDTO>> liveBroadcastPage(RaceLiveBroadcastSearchDTO searchDTO) {
                log.error("RaceActivityClient.liveBroadcastPage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<RaceLiveBroadcastDTO> getLiveBroadcastById(Long id) {
                log.error("RaceActivityClient.getLiveBroadcastById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteLiveBroadcast(Long id) {
                log.error("RaceActivityClient.deleteLiveBroadcast() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<RaceLiveBroadcastDTO> getLatestLiveBroadcastByActivityId(Long activityId) {
                log.error("RaceActivityClient.getLatestLiveBroadcastByActivityId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}