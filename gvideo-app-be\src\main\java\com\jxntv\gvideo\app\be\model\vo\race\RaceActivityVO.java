package com.jxntv.gvideo.app.be.model.vo.race;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 比赛活动前端展示对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "比赛活动VO", description = "比赛活动VO")
public class RaceActivityVO {

    @ApiModelProperty(value = "活动id")
    private Long id;

    @ApiModelProperty(value = "活动标题")
    private String title;

    /**
     * 活动简介
     */
    @ApiModelProperty(value = "活动简介")
    private String introduction;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Long endTime;

    /**
     * 活动状态：0-未开始、1-进行中、2-已结束
     */
    @ApiModelProperty(value = "活动状态：0-未开始、1-进行中、2-已结束")
    private Integer status;

    @ApiModelProperty(value = "活动配置信息")
    private RaceActivityConfigVO config;

    @ApiModelProperty(value = "赛事直播信息")
    private RaceLiveBroadcastVO liveBroadcast;

}
