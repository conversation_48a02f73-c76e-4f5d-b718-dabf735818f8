package com.jxntv.gvideo.media.api.job;

import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.media.api.domain.entity.RaceSchedule;
import com.jxntv.gvideo.media.api.event.RaceScheduleSubscribePushEvent;
import com.jxntv.gvideo.media.api.service.RaceScheduleService;
import com.jxntv.gvideo.media.api.utils.DateUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/16 16:04
 */
@Slf4j
@Component
@RefreshScope
public class RaceActivityJob {

    @Resource
    private RaceScheduleService raceScheduleService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @XxlJob("raceSchedulePushMsgJob")
    public void raceSchedulePushMsgJob() {
        log.info("球赛-赛程预约推送任务开始...");
        String jobParam = XxlJobHelper.getJobParam();
        int aheadMinutes = NumberUtils.isNumber(jobParam) ? Integer.parseInt(jobParam) : 5;

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime aheadTime = now.plusMinutes(aheadMinutes);

        LambdaQueryWrapper<RaceSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.between(RaceSchedule::getStartTime, DateUtils.format(now), DateUtils.format(aheadTime));
        queryWrapper.eq(RaceSchedule::getStatus, 0);
        queryWrapper.eq(RaceSchedule::getDelFlag, 0);
        List<RaceSchedule> list = raceScheduleService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            for (RaceSchedule raceSchedule : list) {
                applicationEventPublisher.publishEvent(new RaceScheduleSubscribePushEvent(raceSchedule));
            }

        }
    }

    @XxlJob("raceScheduleStatusJob")
    public void raceScheduleStatusJob() {
        log.info("球赛-赛程状态更新任务开始...");
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<RaceSchedule> updateWrapper = null;

        // 状态为0，当前时间大于开始时间
        updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(RaceSchedule::getStatus, 1);
        updateWrapper.ne(RaceSchedule::getStatus, 1);
        updateWrapper.lt(RaceSchedule::getStartTime, now);
        raceScheduleService.update(updateWrapper);


        // 状态为1，当前时间大于结束时间
        updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(RaceSchedule::getStatus, 2);
        updateWrapper.ne(RaceSchedule::getStatus, 2);
        updateWrapper.lt(RaceSchedule::getEndTime, now);
        raceScheduleService.update(updateWrapper);

    }


    @XxlJob("raceScheduleCheerJob")
    public void raceScheduleCheerJob() {
        log.info("球赛-队伍助威数初始化任务开始...");
        String jobParam = XxlJobHelper.getJobParam();
        long activityId = NumberUtils.isNumber(jobParam) ? Long.parseLong(jobParam) : 1001L;
        raceScheduleService.initScheduleCheer(activityId);
    }
}
