package com.jxntv.gvideo.media.client.dto.race;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 比赛直播数据传输对象 对应比赛直播表(race_live_broadcast)
 *
 * <AUTHOR>
 */
@Data
public class RaceLiveBroadcastDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 队伍id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 标题
     */
    private String title;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 删除标志 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 创建用户账号
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 更新用户id
     */
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}