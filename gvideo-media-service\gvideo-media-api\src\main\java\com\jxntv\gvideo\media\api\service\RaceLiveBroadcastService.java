package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.media.api.domain.entity.RaceLiveBroadcast;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastSearchDTO;

/**
 * 比赛直播服务接口
 *
 * <AUTHOR>
 */
public interface RaceLiveBroadcastService extends IService<RaceLiveBroadcast> {

    /**
     * 新增比赛直播信息
     *
     * @param dto 比赛直播信息
     * @return 处理结果
     */
    Boolean addLiveBroadcast(RaceLiveBroadcastDTO dto);

    /**
     * 修改比赛直播信息
     *
     * @param dto 比赛直播信息
     * @return 处理结果
     */
    Boolean updateLiveBroadcast(RaceLiveBroadcastDTO dto);

    /**
     * 分页查询比赛直播列表
     *
     * @param searchDTO 查询条件
     * @return 比赛直播分页数据
     */
    PageDTO<RaceLiveBroadcastDTO> page(RaceLiveBroadcastSearchDTO searchDTO);

    /**
     * 比赛直播详情
     *
     * @param id 比赛直播ID
     * @return 比赛直播详情信息
     */
    RaceLiveBroadcastDTO getLiveBroadcastById(Long id);

    /**
     * 删除比赛直播
     *
     * @param id 比赛直播ID
     * @return 删除结果
     */
    Boolean deleteLiveBroadcast(Long id);

    /**
     * 根据活动ID查询最新的活动直播
     * 查询当前时间在比赛直播的开始时间及结束时间之间的直播，如果有多条符合的直播，则取ID较大的那条
     *
     * @param activityId 活动ID
     * @return 比赛直播详情信息
     */
    RaceLiveBroadcastDTO getLatestLiveBroadcastByActivityId(Long activityId);
}