package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MediaResourceExternalClient;
import com.jxntv.gvideo.media.client.dto.AddMediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalCategoryDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class MediaResourceExternalClientFallback implements FallbackFactory<MediaResourceExternalClient> {
    @Override
    public MediaResourceExternalClient create(Throwable throwable) {
        return new MediaResourceExternalClient() {
            @Override
            public Result<MediaResourceExternalDTO> getByMediaId(Long mediaId) {
                log.error("MediaResourceExternalClient.getByMediaId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaResourceExternalDTO> getByContentId(String contentId) {
                log.error("MediaResourceExternalClient.getByContentId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> addExternal(AddMediaResourceExternalDTO dto) {
                log.error("MediaResourceExternalClient.addExternal() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceExternalCategoryDTO>> getCategoryByMediaId(Long mediaId) {
                log.error("MediaResourceExternalClient.getCategoryByMediaId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceExternalDTO>> batchGetByMediaIds(List<Long> mediaIds) {
                log.error("MediaResourceExternalClient.batchGetByMediaIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceExternalCategoryDTO>> batchGetCategoryByMediaIds(List<Long> mediaIds) {
                log.error("MediaResourceExternalClient.batchGetCategoryByMediaIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

        };
    }
}
