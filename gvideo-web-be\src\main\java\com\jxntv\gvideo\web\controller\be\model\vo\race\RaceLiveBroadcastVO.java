package com.jxntv.gvideo.web.controller.be.model.vo.race;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 比赛直播VO对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RaceLiveBroadcastVO", description = "比赛直播VO对象")
public class RaceLiveBroadcastVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "资源标题")
    private String resourceTitle;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 创建用户id
     */
    @ApiModelProperty(value = "创建用户id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @ApiModelProperty(value = "创建用户账号")
    private String createUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createDate;

    /**
     * 更新用户id
     */
    @ApiModelProperty(value = "更新用户id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @ApiModelProperty(value = "更新用户账号")
    private String updateUserName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateDate;
}
