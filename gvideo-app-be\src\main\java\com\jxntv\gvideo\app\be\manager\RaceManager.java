package com.jxntv.gvideo.app.be.manager;

import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.media.client.RaceActivityClient;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamDTO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RaceManager {

    @Resource
    private RaceActivityClient raceActivityClient;

    @Cached(name = "app::race::team::byId", cacheType = CacheType.BOTH, timeUnit = TimeUnit.MINUTES, expire = 10)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public RaceTeamDTO getTeamId(Long teamId) {
        return raceActivityClient.getTeamById(teamId).orElse(null);
    }

    @Cached(name = "app::race::getLatestLiveBroadcast", cacheType = CacheType.BOTH, timeUnit = TimeUnit.MINUTES, expire = 10)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public RaceLiveBroadcastDTO getLatestLiveBroadcast(Long activityId) {
        return raceActivityClient.getLatestLiveBroadcastByActivityId(activityId).orElse(null);
    }
}
