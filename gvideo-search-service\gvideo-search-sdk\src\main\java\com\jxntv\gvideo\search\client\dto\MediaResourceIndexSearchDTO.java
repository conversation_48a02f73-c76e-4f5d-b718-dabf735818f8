package com.jxntv.gvideo.search.client.dto;

import java.util.Date;
import java.util.List;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MediaResourceIndexSearchDTO extends SearchDTO {
    /**
     * 标签过滤
     */
    private List<LabelFilter> labelFilters;
    /**
     * 分类名称
     */
    private List<Long> categoryNames;
    /**
     * 分类ID
     */
    private List<Long> categoryIds;

    /**
     * 状态
     */
    private List<Integer> status;
    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;

    @Data
    public static class LabelFilter {
        private Long id;
        private Integer type;
        private String name;
    }


}
