package com.jxntv.gvideo.media.api.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceExternal;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/17 10:10
 */
public interface MediaResourceExternalService extends IService<MediaResourceExternal> {

    MediaResourceExternal geExternal(String platform, String contentId);

    MediaResourceExternal getByMediaId(Long mediaId);

    boolean create(MediaResourceExternalDTO dto);

    boolean update(MediaResourceExternalDTO dto);

    List<MediaResourceExternal> listByMediaIds(List<Long> mediaIds);

}
