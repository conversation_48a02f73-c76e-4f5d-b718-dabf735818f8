package com.jxntv.gvideo.app.be.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.converter.race.RaceActivityConverter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.model.vo.race.RaceActivityVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceScheduleSubscribeVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceScheduleVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceTeamCheerVO;
import com.jxntv.gvideo.common.constants.RedisConstants;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.RaceActivityClient;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleSearchDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 球赛活动控制器 - App端 提供球赛活动相关的API接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/race/activity")
@Api(value = "球赛活动相关接口", tags = {"球赛活动相关接口"})
public class RaceActivityController {

    @Resource
    private RaceActivityClient raceActivityClient;

    @Resource
    private RaceActivityConverter raceActivityConverter;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 通过活动ID查询活动信息
     *
     */
    @GetMapping("/{activityId}")
    @ApiOperation(value = "查询活动信息", notes = "通过活动ID查询活动信息")
    public Result<RaceActivityVO> getSchedulePage(@PathVariable Long activityId) {
        if (Objects.isNull(activityId)) {
            return Result.fail("活动ID不能为空");
        }
        // 调用远程服务查询活动数据
        Result<RaceActivityDTO> result = raceActivityClient.getActivityById(activityId);
        if (!result.callSuccess() || Objects.isNull(result.getResult())) {
            log.error("查询活动信息失败，activityId: {}, error: {}", activityId, result.getMessage());
            return Result.fail(result.getCode(), "查询活动信息失败");
        }

        return Result.ok(raceActivityConverter.convert(result.getResult()));
    }


    /**
     * 通过活动ID分页查询赛程信息
     *
     * @param activityId 活动ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 赛程分页数据
     */
    @GetMapping("/schedule/page")
    @ApiOperation(value = "分页查询活动赛程信息", notes = "通过活动ID分页查询赛程信息")
    public Result<Page<RaceScheduleVO>> getSchedulePage(@ApiParam(name = "activityId", value = "活动ID", required = true) @RequestParam Long activityId, @ApiParam(name = "pageNum", value = "页码", required = false, defaultValue = "1") @RequestParam(required = false, defaultValue = "1") Integer pageNum, @ApiParam(name = "pageSize", value = "每页大小", required = false, defaultValue = "10") @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        // 参数校验
        if (Objects.isNull(activityId)) {
            return Result.fail("活动ID不能为空");
        }
        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize <= 0 || pageSize > 200) {
            pageSize = 10;
        }

        // 构建查询条件
        RaceScheduleSearchDTO searchDTO = new RaceScheduleSearchDTO();
        searchDTO.setActivityId(activityId);
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setSorts(Arrays.asList(SearchDTO.Sort.of("startTime", true), SearchDTO.Sort.of("id", false)));
        // 调用远程服务查询赛程数据
        Result<PageDTO<RaceScheduleDTO>> result = raceActivityClient.schedulePage(searchDTO);
        if (!result.callSuccess() || Objects.isNull(result.getResult())) {
            log.error("查询活动赛程失败，activityId: {}, error: {}", activityId, result.getMessage());
            return Result.fail(result.getCode(), "查询活动赛程信息失败");
        }
        Long jid = ThreadLocalCache.getJid();

        return Result.ok(Page.Of(result.getResult(), e -> raceActivityConverter.convert(e, jid)));
    }

    /**
     * 预约赛程
     *
     * @param vo 预约信息
     * @return 预约结果
     */
    @PostMapping("/schedule/subscribe")
    @ApiOperation(value = "预约（取消预约）赛程", notes = "预约（取消预约）赛程")
    public Result<Void> subscribeSchedule(@RequestBody @Validated RaceScheduleSubscribeVO vo) {

        // 获取当前登录用户信息
        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getJid())) {
            return Result.fail("获取用户信息失败");
        }

        RaceScheduleDTO raceScheduleDTO = raceActivityClient.getScheduleById(vo.getScheduleId()).orElse(null);
        if (Objects.isNull(raceScheduleDTO)) {
            return Result.fail("赛程信息不存在");
        }
        if (!Objects.equals(0, raceScheduleDTO.getStatus())) {
            return Result.fail("赛程已开始或结束，无法预约");
        }
        String jid = String.valueOf(userInfo.getJid());

        String key = String.format(RedisConstants.RACE_ACTIVITY_SCHEDULE_SUBSCRIBE_KEY, vo.getScheduleId());
        if (Objects.equals(1, vo.getType())) {
            // 预约
            stringRedisTemplate.opsForSet().add(key, jid);
        } else {
            // 取消预约
            stringRedisTemplate.opsForSet().remove(key, jid);
        }
        return Result.ok();
    }

    /**
     * 赛程队伍助威
     *
     * @param vo 助威请求参数
     * @return 助威结果
     */
    @PostMapping("/team/cheer")
    @ApiOperation(value = "赛程队伍助威", notes = "用户为指定赛程下的队伍进行助威，每天每个队伍最多助威10次")
    public Result<Void> cheerTeam(@RequestBody @Validated RaceTeamCheerVO vo) {

        // 获取当前登录用户信息
        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getJid())) {
            return Result.fail("获取用户信息失败");
        }

        Long jid = userInfo.getJid();
        Long scheduleId = vo.getScheduleId();
        Long teamId = vo.getTeamId();

        // 验证赛程是否存在
        RaceScheduleDTO raceScheduleDTO = raceActivityClient.getScheduleById(scheduleId).orElse(null);
        if (Objects.isNull(raceScheduleDTO) || Objects.equals(1, raceScheduleDTO.getDelFlag())) {
            return Result.fail("赛程信息不存在");
        }

        // 检查赛程状态，只有未开始和进行中的赛程可以助威
        if (Objects.equals(2, raceScheduleDTO.getStatus())) {
            return Result.fail("赛程已结束，无法助威");
        }

        // 验证队伍是否属于该赛程
        if (!Objects.equals(teamId, raceScheduleDTO.getHomeTeamId()) && !Objects.equals(teamId, raceScheduleDTO.getAwayTeamId())) {
            return Result.fail("助威队伍不属于该赛程");
        }

        RaceActivityDTO raceActivityDTO = raceActivityClient.getActivityById(raceScheduleDTO.getActivityId()).orElse(null);
        if (Objects.isNull(raceActivityDTO) || Objects.equals(1, raceActivityDTO.getDelFlag())) {
            return Result.fail("比赛活动信息不存在");
        }

        Integer maxCheerTimes = Objects.nonNull(raceActivityDTO.getConfig()) && Objects.nonNull(raceActivityDTO.getConfig().getMaxCheerTimes()) && raceActivityDTO.getConfig().getMaxCheerTimes() > 0 ? raceActivityDTO.getConfig().getMaxCheerTimes() : 10;

        // 构建用户每日助威次数Redis键
        String userDailyCheerKey = String.format(RedisConstants.RACE_ACTIVITY_SCHEDULE_USER_DAILY_CHEER_KEY, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), scheduleId, teamId, jid);

        // 原子性增加用户每日助威次数（先执行increment，再检查是否超限）
        Long newDailyCount = stringRedisTemplate.opsForValue().increment(userDailyCheerKey);

        // 检查是否超过每日助威限制
        if (newDailyCount > maxCheerTimes) {
            // 超限时回滚计数器
            stringRedisTemplate.opsForValue().decrement(userDailyCheerKey);
            return Result.fail("每天最多为它助威" + maxCheerTimes + "次");
        }

        // 设置键的过期时间为当天结束（第二天凌晨重置）
        if (newDailyCount == 1) {
            // 只在第一次设置时添加过期时间，避免重复设置
            stringRedisTemplate.expire(userDailyCheerKey, 1, TimeUnit.DAYS);
        }

        // 增加队伍总助威数
        String teamCheerKey = String.format(RedisConstants.RACE_ACTIVITY_SCHEDULE_CHEER_KEY, scheduleId, teamId);
        stringRedisTemplate.opsForValue().increment(teamCheerKey);

        return Result.ok();
    }
}
