package com.jxntv.gvideo.media.api.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 红活动报名
 */
@Data
@TableName("red_activity_active")
public class RedActivityActive {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 报名类型：机构、个人
     */
    private String type;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 作品类型
     */
    private String productType;

    /**
     * 作品简介
     */
    private String productIntro;

    /**
     * 作品时间
     */
    private String productDate;

    /**
     * 创作者
     */
    private String creators;

    /**
     * 第一主创人工作单位
     */
    private String firstCreatorCompany;

    /**
     * 第一主创人省份
     */
    private String firstCreatorProvince;

    /**
     * 第一主创人职业
     */
    private String firstCreatorOccupation;

    /**
     * 第一主创人民族
     */
    private String firstCreatorEthnic;

    /**
     * 第一主创人性别：0-男，1-女
     */
    private Integer firstCreatorGender = 0;

    /**
     * 第一主创人年龄
     */
    private Integer firstCreatorAge;

    /**
     * 创办单位
     */
    private String founders;

    /**
     * 发布状态
     */
    private String releaseState;

    /**
     * 发布日期
     */
    private String releaseDate;

    /**
     * 文档Url
     */
    private String docUrl;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 转码后的标清视频
     */
    private String bqVideoUrl;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 作品评分：1:未打分，2：已打分
     */
    private BigDecimal score;

    /**
     * 评分状态：1：已报名，10：展播
     */
    private Integer state;

    /**
     * 复评状态：0-未复评 1：未通过，2：已通过
     */
    private Integer reState;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
