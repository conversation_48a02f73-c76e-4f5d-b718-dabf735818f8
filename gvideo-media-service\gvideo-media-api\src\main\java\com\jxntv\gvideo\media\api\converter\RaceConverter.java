package com.jxntv.gvideo.media.api.converter;

import java.util.Objects;
import org.springframework.stereotype.Component;
import com.jxntv.gvideo.media.api.domain.entity.RaceActivity;
import com.jxntv.gvideo.media.api.domain.entity.RaceLiveBroadcast;
import com.jxntv.gvideo.media.api.domain.entity.RaceSchedule;
import com.jxntv.gvideo.media.api.domain.entity.RaceTeam;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 比赛相关转换器 用于entity和dto之间的数据转换 包含活动、队伍、赛程、预约等所有转换方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RaceConverter {

    // ==================== RaceActivity 转换方法 ====================

    /**
     * 活动实体转DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    public RaceActivityDTO convert(RaceActivity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        RaceActivityDTO dto = new RaceActivityDTO();
        dto.setId(entity.getId());
        dto.setTitle(entity.getTitle());
        dto.setIntroduction(entity.getIntroduction());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setStatus(entity.getStatus());
        dto.setDelFlag(entity.getDelFlag());
        dto.setConfig(entity.getConfig());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

    /**
     * 活动DTO转实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    public RaceActivity convert(RaceActivityDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceActivity entity = new RaceActivity();
        entity.setId(dto.getId());
        entity.setTitle(dto.getTitle());
        entity.setIntroduction(dto.getIntroduction());
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setStatus(dto.getStatus());
        entity.setDelFlag(dto.getDelFlag());
        entity.setConfig(dto.getConfig());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }

    // ==================== RaceTeam 转换方法 ====================

    /**
     * 队伍实体转DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    public RaceTeamDTO convert(RaceTeam entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        RaceTeamDTO dto = new RaceTeamDTO();
        dto.setId(entity.getId());
        dto.setActivityId(entity.getActivityId());
        dto.setTitle(entity.getTitle());
        dto.setIntroduction(entity.getIntroduction());
        dto.setIcon(entity.getIcon());
        dto.setDelFlag(entity.getDelFlag());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

    /**
     * 队伍DTO转实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    public RaceTeam convert(RaceTeamDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceTeam entity = new RaceTeam();
        entity.setId(dto.getId());
        entity.setActivityId(dto.getActivityId());
        entity.setTitle(dto.getTitle());
        entity.setIntroduction(dto.getIntroduction());
        entity.setIcon(dto.getIcon());
        entity.setDelFlag(dto.getDelFlag());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }

    // ==================== RaceSchedule 转换方法 ====================

    /**
     * 赛程实体转DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    public RaceScheduleDTO convert(RaceSchedule entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        RaceScheduleDTO dto = new RaceScheduleDTO();
        dto.setId(entity.getId());
        dto.setActivityId(entity.getActivityId());
        dto.setTitle(entity.getTitle());
        dto.setIntroduction(entity.getIntroduction());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setHomeTeamId(entity.getHomeTeamId());
        dto.setAwayTeamId(entity.getAwayTeamId());
        dto.setHomeTeamScore(entity.getHomeTeamScore());
        dto.setAwayTeamScore(entity.getAwayTeamScore());
        dto.setResourceId(entity.getResourceId());
        dto.setPushTitle(entity.getPushTitle());
        dto.setPushContent(entity.getPushContent());
        dto.setStatus(entity.getStatus());
        dto.setDelFlag(entity.getDelFlag());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        dto.setHomeTeamCheer(entity.getHomeTeamCheer());
        dto.setAwayTeamCheer(entity.getAwayTeamCheer());
        return dto;
    }

    /**
     * 赛程DTO转实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    public RaceSchedule convert(RaceScheduleDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceSchedule entity = new RaceSchedule();
        entity.setId(dto.getId());
        entity.setActivityId(dto.getActivityId());
        entity.setTitle(dto.getTitle());
        entity.setIntroduction(dto.getIntroduction());
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setHomeTeamId(dto.getHomeTeamId());
        entity.setAwayTeamId(dto.getAwayTeamId());
        entity.setHomeTeamScore(dto.getHomeTeamScore());
        entity.setAwayTeamScore(dto.getAwayTeamScore());
        entity.setResourceId(dto.getResourceId());
        entity.setPushTitle(dto.getPushTitle());
        entity.setPushContent(dto.getPushContent());
        entity.setStatus(dto.getStatus());
        entity.setDelFlag(dto.getDelFlag());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        entity.setHomeTeamCheer(dto.getHomeTeamCheer());
        entity.setAwayTeamCheer(dto.getAwayTeamCheer());
        return entity;
    }

    // ==================== RaceLiveBroadcast 转换方法 ====================

    /**
     * 比赛直播实体转DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    public RaceLiveBroadcastDTO convertToDto(RaceLiveBroadcast entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        RaceLiveBroadcastDTO dto = new RaceLiveBroadcastDTO();
        dto.setId(entity.getId());
        dto.setActivityId(entity.getActivityId());
        dto.setResourceId(entity.getResourceId());
        dto.setTitle(entity.getTitle());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setDelFlag(entity.getDelFlag());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setCreateUserName(entity.getCreateUserName());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setUpdateUserName(entity.getUpdateUserName());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

    /**
     * 比赛直播DTO转实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    public RaceLiveBroadcast convertToEntity(RaceLiveBroadcastDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceLiveBroadcast entity = new RaceLiveBroadcast();
        entity.setId(dto.getId());
        entity.setActivityId(dto.getActivityId());
        entity.setResourceId(dto.getResourceId());
        entity.setTitle(dto.getTitle());
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setDelFlag(dto.getDelFlag());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }
}
