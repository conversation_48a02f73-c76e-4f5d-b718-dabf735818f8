package com.jxntv.gvideo.search.api.event.listener;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.search.api.event.BinlogBatchEvent;
import com.jxntv.gvideo.search.api.event.BinlogEvent;
import com.jxntv.gvideo.search.api.service.MediaResourceIndexService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MediaResourceIndexListener {

    @Resource
    private MediaResourceIndexService mediaResourceIndexService;

    @EventListener
    public void onEvent(BinlogBatchEvent binlogBatchEvent) {
        String table = binlogBatchEvent.getTable();
        List<BinlogEvent> events = binlogBatchEvent.getEvents();

        switch (table) {
            case "media_resource":
                this.handleMediaResourceEvents(events);
                break;
            case "media_resource_label":
                this.handleMediaResourceLabelEvents(events);
                break;
            case "media_resource_ganyun_relate":
                this.handleMediaResourceExternalEvents(events);
                break;
            case "media_resource_ganyun_category":
                this.handleMediaResourceExternalCategoryEvents(events);
                break;
            default:
                break;
        }

    }

    /**
     * 处理media-resource索引事件
     *
     * @param event 事件
     */
    private void handleMediaResourceEvents(List<BinlogEvent> events) {
        try {
            // 批量删除索引
            List<BinlogEvent> deleteEvents = events.stream()
                    .filter(e -> BinlogEventType.DELETE.equals(e.getEventType())).collect(Collectors.toList());
            if (!deleteEvents.isEmpty()) {
                mediaResourceIndexService.removeByIds(
                        deleteEvents.stream().map(e -> e.getBefore().getString("id")).collect(Collectors.toList()));

                log.debug("删除media-resource索引, resourceId:{}",
                        deleteEvents.stream().map(e -> e.getBefore().getString("id")).collect(Collectors.joining(",")));
            }
            // 批量新增索引
            List<BinlogEvent> insertEvents = events.stream()
                    .filter(e -> BinlogEventType.INSERT.equals(e.getEventType())).collect(Collectors.toList());
            if (!insertEvents.isEmpty()) {
                mediaResourceIndexService.batchIndexByIds(
                        insertEvents.stream().map(e -> e.getAfter().getLong("id")).collect(Collectors.toList()));
                log.debug("新增media-resource索引, resourceId:{}",
                        insertEvents.stream().map(e -> e.getAfter().getString("id")).collect(Collectors.joining(",")));
            }
            // 批量刷新索引
            List<BinlogEvent> updateEvents = events.stream()
                    .filter(e -> BinlogEventType.UPDATE.equals(e.getEventType())).collect(Collectors.toList());
            if (!updateEvents.isEmpty()) {
                mediaResourceIndexService.batchIndexByIds(
                        updateEvents.stream().map(e -> e.getAfter().getLong("id")).collect(Collectors.toList()));
                log.debug("刷新media-resource索引, resourceId:{}",
                        updateEvents.stream().map(e -> e.getAfter().getString("id")).collect(Collectors.joining(",")));
            }
        } catch (Exception e) {
            log.error("处理media-resource事件异常,events:{}", JSON.toJSONString(events), e);
        }
    }

    /**
     * 资源扩展表
     *
     * @param event 事件
     */

    private void handleMediaResourceExternalEvents(List<BinlogEvent> events) {
        try {
            List<Long> ids = events.stream()
            .map(e -> BinlogEventType.DELETE.equals(e.getEventType()) ? e.getBefore().getLong("mediaId")
                    : e.getAfter().getLong("mediaId"))
            .distinct()
            .collect(Collectors.toList());
            // 刷新索引
            mediaResourceIndexService.batchIndexByIds(ids);
            log.debug("刷新media-resource索引, resourceId:{}", ids);

        } catch (Exception e) {
            log.error("处理media-resource索引失败, events:{}", JSON.toJSONString(events), e);
        }
    }

    /**
     * 资源扩展分类表
     *
     * @param event 事件
     */

    private void handleMediaResourceExternalCategoryEvents(List<BinlogEvent> events) {
        try {
            List<Long> ids = events.stream()
            .map(e -> BinlogEventType.DELETE.equals(e.getEventType()) ? e.getBefore().getLong("mediaId")
                    : e.getAfter().getLong("mediaId"))
            .distinct()
            .collect(Collectors.toList());

            // 刷新索引
            mediaResourceIndexService.batchIndexByIds(ids);
            log.debug("刷新media-resource索引, resourceId:{}", ids);

        } catch (Exception e) {
            log.error("处理media-resource索引失败, events:{}", JSON.toJSONString(events), e);
        }
    }

    /**
     * 资源标签表
     *
     * @param event 事件
     */

    private void handleMediaResourceLabelEvents(List<BinlogEvent> events) {
        try {
            List<Long> ids = events.stream()
                    .map(e -> BinlogEventType.DELETE.equals(e.getEventType()) ? e.getBefore().getLong("resourceId")
                            : e.getAfter().getLong("resourceId"))
                    .distinct()
                    .collect(Collectors.toList());

            // 刷新索引
            mediaResourceIndexService.batchIndexByIds(ids);
            log.debug("刷新media-resource索引, resourceId:{}", ids);

        } catch (Exception e) {
            log.error("处理media-resource索引失败, events:{}", JSON.toJSONString(events), e);
        }
    }
}
