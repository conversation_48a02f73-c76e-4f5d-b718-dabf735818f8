package com.jxntv.gvideo.app.be.model.vo.race;

import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 赛事直播VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RaceLiveBroadcastVO", description = "赛事直播VO")
public class RaceLiveBroadcastVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "资源标题")
    private String mediaTitle;

    @ApiModelProperty(value = "资源大类 1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏 9 图文 10 语音 11 文章大图/三图 12 文章左文右图 13 外链 14 特殊展示块 15 滚动文章块 17 横向滚动块 23 自建频道直播", required = true, example = "1", notes = "1视频2FM")
    private Integer mediaType;

    @ApiModelProperty(value = "资源链接集合", required = true)
    private List<String> mediaUrls;
}
