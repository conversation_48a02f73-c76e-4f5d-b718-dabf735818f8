package com.jxntv.gvideo.media.api.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.aliyun.sdk.param.SendCodeParam;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.client.ValidateService;
import com.jxntv.gvideo.media.api.domain.entity.RedActivity;
import com.jxntv.gvideo.media.api.domain.entity.RedActivityActive;
import com.jxntv.gvideo.media.api.domain.entity.RedActivityJudge;
import com.jxntv.gvideo.media.api.domain.entity.RedActivityJudgeScore;
import com.jxntv.gvideo.media.api.repository.RedActivityActiveMapper;
import com.jxntv.gvideo.media.api.repository.RedActivityJudgeMapper;
import com.jxntv.gvideo.media.api.repository.RedActivityJudgeScoreMapper;
import com.jxntv.gvideo.media.api.repository.RedActivityMapper;
import com.jxntv.gvideo.media.api.service.RedActivityService;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityActiveDTO;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityAuditingParam;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityDTO;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityJudgeDTO;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityQueryParam;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityScoringParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 红色活动服务实现
 */
@Service
@Slf4j
public class RedActivityServiceImpl implements RedActivityService {

    @Resource
    private ValidateService validateService;

    @Resource
    private RedActivityMapper redActivityMapper;

    @Resource
    private RedActivityActiveMapper redActivityActiveMapper;

    @Resource
    private RedActivityJudgeMapper redActivityJudgeMapper;

    @Resource
    private RedActivityJudgeScoreMapper scoreMapper;

    private final static String TEMPLATE_CODE = "SMS_490590218";

    /**
     * redisKey
     */
    private final static String REDIS_KEY = "RedActivity:";
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Result<String> sendCode(String mobile) {
        SendCodeParam param = new SendCodeParam();
        Integer code = new Random().nextInt(899999) + 100000;
        param.setCode(code);
        param.setMobile(mobile);
        param.setTemplateCode(TEMPLATE_CODE);
        Result<Boolean> result = validateService.sendCode(param);
        if (result.callSuccess() && result.getResult()) {
            // 写入redis，60秒到期
            stringRedisTemplate.opsForValue().set(REDIS_KEY + mobile, String.valueOf(code), 60, TimeUnit.SECONDS);
            return Result.ok("发送成功");
        }
        return Result.fail("发送失败");
    }

    @Override
    public Result<String> register(RedActivityActiveDTO dto) {
        // 校验手机验证码
        // if (dto.getMobileCode() == null) {
        // return Result.fail("请输入验证码");
        // }
        // String code = stringRedisTemplate.opsForValue().get(REDIS_KEY + dto.getMobile());
        // if (Objects.isNull(code)) {
        // return Result.fail("验证码已过期，请重新发送");
        // }
        // if (!code.equals(dto.getMobileCode())) {
        // return Result.fail("验证码错误");
        // }

        RedActivityActive active = new RedActivityActive();
        BeanUtils.copyProperties(dto, active);
        redActivityActiveMapper.insert(active);
        return Result.ok("报名成功");
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listProject(RedActivityQueryParam queryParam) {
        // 查询评委赛道
        RedActivityJudge redActivityJude = getJudge(queryParam.getJudgePhone(), "初审");
        if (redActivityJude == null) {
            return Result.fail("该评委未配置赛道");
        }
        // 根据赛道查询是否打分
        if (redActivityJude.getJudgeTrack() != null && !CollectionUtils.isEmpty(redActivityJude.getJudgeTrack().getProductIds())) {
            queryParam.setProductIdList(redActivityJude.getJudgeTrack().getProductIds());
        }

        queryParam.setJudgeId(redActivityJude.getId());
        queryParam.setType(queryParam.getType());

        /*
         * if (StringUtils.isNotBlank(queryParam.getProductType())) { queryParam.setOutProductTypeList(Arrays.asList(queryParam.getProductType().split(","))); }
         */

        Page<RedActivityActiveDTO> page1 = new Page<>(queryParam.getPage(), queryParam.getPageSize());
        IPage<RedActivityActiveDTO> resultPage = redActivityActiveMapper.selectByParam(page1, queryParam);
        PageDTO<RedActivityActiveDTO> pageDTO = new PageDTO<>();
        pageDTO.setTotal((int) resultPage.getTotal());
        pageDTO.setList(resultPage.getRecords().stream().map(this::fillUrl).collect(Collectors.toList()));
        return Result.ok(pageDTO);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listFinalProject(RedActivityQueryParam queryParam) {
        // 查询评委赛道
        RedActivityJudge redActivityJude = getJudge(queryParam.getJudgePhone(), "终评");
        if (redActivityJude == null) {
            return Result.fail("该评委未配置赛道");
        }
        // 根据赛道查询是否打分
        if (redActivityJude.getJudgeTrack() != null) {
            queryParam.setProductIdList(redActivityJude.getJudgeTrack().getProductIds());
        }
        queryParam.setJudgeId(redActivityJude.getId());
        queryParam.setType(queryParam.getType());
        /*
         * if (StringUtils.isNotBlank(queryParam.getProductType())) { queryParam.setOutProductTypeList(Arrays.asList(queryParam.getProductType().split(","))); }
         */

        Page<RedActivityActiveDTO> page1 = new Page<>(queryParam.getPage(), queryParam.getPageSize());
        IPage<RedActivityActiveDTO> resultPage = redActivityActiveMapper.selectFinalProjects(page1, queryParam);
        PageDTO<RedActivityActiveDTO> pageDTO = new PageDTO<>();
        pageDTO.setTotal((int) resultPage.getTotal());
        pageDTO.setList(resultPage.getRecords().stream().map(this::fillUrl).collect(Collectors.toList()));
        return Result.ok(pageDTO);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listProduct(RedActivityQueryParam queryParam) {
        // 先通过作品反向查评委，再查询评委是否全部打完分，将所有分数查出，去掉一个最高分
        Page<RedActivityActive> page1 = new Page<>(queryParam.getPage(), queryParam.getPageSize());
        LambdaQueryWrapper<RedActivityActive> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(queryParam.getType()), RedActivityActive::getType, queryParam.getType());
        queryWrapper.eq(StringUtils.isNotBlank(queryParam.getProductType()), RedActivityActive::getProductType, queryParam.getProductType());
        if (StringUtils.isNotBlank(queryParam.getKeyword())) {
            queryWrapper.like(RedActivityActive::getProductName, "%" + queryParam.getKeyword() + "%").or().like(RedActivityActive::getProductIntro, "%" + queryParam.getKeyword() + "%").or().eq(RedActivityActive::getId, queryParam.getKeyword());;
        }
        queryWrapper.orderByDesc(RedActivityActive::getScore);
        queryWrapper.orderByAsc(RedActivityActive::getId);
        PageDTO<RedActivityActiveDTO> pageDTO = new PageDTO<>();
        IPage<RedActivityActive> resultPage = redActivityActiveMapper.selectPage(page1, queryWrapper);
        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            List<RedActivityActiveDTO> resultList = resultPage.getRecords().stream().map(item -> {
                RedActivityActiveDTO dto = new RedActivityActiveDTO();
                BeanUtils.copyProperties(item, dto);
                fillUrl(dto);

                List<Integer> scores = new ArrayList<>(5);

                List<RedActivityJudgeScore> scoreList = scoreMapper.selectList(new LambdaQueryWrapper<RedActivityJudgeScore>().eq(RedActivityJudgeScore::getProductId, item.getId()).orderByAsc(RedActivityJudgeScore::getId).last("limit 5"));

                // 填充评分数据，不足5条则补0
                for (int i = 0; i < scores.size(); i++) {
                    if (!CollectionUtils.isEmpty(scoreList) && i < scoreList.size()) {
                        scores.add(scoreList.get(i).getScore());
                    } else {
                        scores.add(0);
                    }
                }
                dto.setJudgeScores(scores);
                return dto;
            }).collect(Collectors.toList());
            pageDTO.setTotal((int) resultPage.getTotal());
            pageDTO.setList(resultList);
            return Result.ok(pageDTO);
        }
        return Result.ok(pageDTO);
    }

    @Override
    public Result<List<RedActivityActiveDTO>> query(RedActivityQueryParam queryParam) {
        LambdaQueryWrapper<RedActivityActive> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(RedActivityActive::getId, RedActivityActive::getProductName, RedActivityActive::getCreateTime);
        queryWrapper.eq(RedActivityActive::getMobile, queryParam.getMobile());
        queryWrapper.orderByAsc(RedActivityActive::getCreateTime);
        List<RedActivityActive> list = redActivityActiveMapper.selectList(queryWrapper);
        if (list != null && !list.isEmpty()) {
            return Result.ok(list.stream().map(item -> {
                RedActivityActiveDTO dto = new RedActivityActiveDTO();
                BeanUtils.copyProperties(item, dto);
                return dto;
            }).collect(Collectors.toList()));
        }
        return Result.ok(Collections.emptyList());
    }

    @Override
    public Result<String> scoring(RedActivityScoringParam param) {
        // 查询是否存在该作品的打分纪录，一个作品只能打分一次
        RedActivityJudge RedActivityJudge = getJudge(param.getJudgeMobile(), "初审");
        if (RedActivityJudge == null) {
            return Result.fail("评委不存在");
        }
        RedActivityJudgeScore score = getScore(param.getProductId(), RedActivityJudge.getId());
        if (score == null) {
            // 新建
            score = new RedActivityJudgeScore();
            score.setProductId(param.getProductId());
            score.setJudgeId(RedActivityJudge.getId());
            score.setState(2); // 设置为已打分状态
            score.setScore(param.getScore());
            scoreMapper.insert(score);
        } else if (score.getState() == 2) {
            score.setScore(param.getScore());
            score.setProductId(param.getProductId());
            score.setState(2); // 设置为已锁定状态
            scoreMapper.updateById(score);
        } else if (score.getState() == 3) {
            return Result.fail("结果已锁定，无法修改");
        }
        // 存在则更新分数，不存在则创建纪录
        return Result.ok("打分成功");
    }

    @Override
    public Result<String> submit(RedActivityScoringParam param) {
        RedActivityJudge judge = getJudge(param.getJudgeMobile(), null);
        if (judge == null) {
            return Result.fail("评委不存在");
        }
        // 判断是否存在作品没打分
        RedActivityQueryParam param1 = new RedActivityQueryParam();
        param1.setJudgePhone(param.getJudgeMobile());
        param1.setPage(1);
        param1.setPageSize(1);
        param1.setState(1); // 查询未打分的数量

        Result<PageDTO<RedActivityActiveDTO>> result = listProject(param1);
        if (!result.callSuccess()) {
            return Result.fail("获取打分作品总数失败");
        }
        // 需要打分的数量
        int total = result.getResult().getTotal();
        if (total > 0) {
            return Result.fail("还有作品未打分");
        }

        // 将该评委的打分纪录全部锁定
        RedActivityJudgeScore score = new RedActivityJudgeScore();
        score.setState(3); // 锁定
        LambdaQueryWrapper<RedActivityJudgeScore> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(RedActivityJudgeScore::getJudgeId, judge.getId());
        scoreMapper.update(score, updateWrapper);
        return Result.ok("结果已提交");
    }

    @Override
    public Result<String> getJudgeTracks(String mobile, String type) {

        /*
         * RedActivityJudge judge = getJudge(mobile, type); if (judge != null && StringUtils.isNotBlank(judge.getJudgeTrack())) { return Result.ok(judge.getJudgeTrack().replace("纪录片,微纪录片", "纪录片与微纪录片").replace("电视剧,微短剧", "电视剧与微短剧").replace("红色故事分享,红色文化传承与创新,红色文物寻踪,红色革命圣地打卡", "全民拍")); }
         */
        return Result.fail("未配置赛道");
    }

    public RedActivityJudgeScore getScore(Long productId, Long judgeId) {
        LambdaQueryWrapper<RedActivityJudgeScore> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RedActivityJudgeScore::getProductId, productId);
        queryWrapper.eq(RedActivityJudgeScore::getJudgeId, judgeId);
        return scoreMapper.selectOne(queryWrapper);
    }

    public RedActivityJudge getJudge(String mobile, String type) {
        LambdaQueryWrapper<RedActivityJudge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RedActivityJudge::getJudgePhone, mobile);
        queryWrapper.eq(StringUtils.isNotBlank(type), RedActivityJudge::getJudgeType, "终评");
        queryWrapper.orderByAsc(RedActivityJudge::getId);
        List<RedActivityJudge> list = redActivityJudgeMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    private RedActivityActiveDTO fillUrl(RedActivityActiveDTO item) {
        if (StringUtils.isNotBlank(item.getDocUrl())) {
            item.setDocUrl("https://hdjbm.jxgdw.com/" + item.getDocUrl());
        }
        if (StringUtils.isNoneBlank(item.getVideoUrl())) {
            item.setVideoUrl("https://hdjbm.jxgdw.com/" + item.getVideoUrl());
        }
        if (StringUtils.isBlank(item.getBqVideoUrl())) {
            item.setBqVideoUrl(item.getVideoUrl());
        } else {
            item.setBqVideoUrl("https://hdjbm.jxgdw.com/" + item.getBqVideoUrl());
        }
        return item;
    }

    @Override
    public Result<RedActivityDTO> getDetailById(Long activityId) {
        RedActivity activity = this.redActivityMapper.selectById(activityId);
        if (activity == null) {
            return Result.fail("活动不存在");
        }

        RedActivityDTO dto = new RedActivityDTO();
        BeanUtils.copyProperties(activity, dto);
        return Result.ok(dto);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listScoredProducts(RedActivityQueryParam queryParam) {
        // 分页查询已打分的作品（state = 2）
        Page<RedActivityActive> page = new Page<>(queryParam.getPage(), queryParam.getPageSize());
        LambdaQueryWrapper<RedActivityActive> queryWrapper = new LambdaQueryWrapper<>();

        // 查询条件：已打分状态
        if (queryParam.getState() == null || queryParam.getState() != 100) {
            queryWrapper.eq(RedActivityActive::getState, 10);
        }

        // 可选条件：活动类型
        if (StringUtils.isNotBlank(queryParam.getType())) {
            queryWrapper.eq(RedActivityActive::getType, queryParam.getType());
        }

        // 可选条件：作品类型/分类
        if (StringUtils.isNotBlank(queryParam.getProductType())) {
            queryWrapper.eq(RedActivityActive::getProductType, queryParam.getProductType());
        }

        // 可选条件：关键词搜索（作品名称、简介、ID）
        if (StringUtils.isNotBlank(queryParam.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper.like(RedActivityActive::getProductName, queryParam.getKeyword()).or().like(RedActivityActive::getProductIntro, queryParam.getKeyword()).or().eq(RedActivityActive::getId, queryParam.getKeyword()));
        }

        // 排序：按创建时间升序
        queryWrapper.orderByAsc(RedActivityActive::getCreateTime);

        IPage<RedActivityActive> resultPage = redActivityActiveMapper.selectPage(page, queryWrapper);

        PageDTO<RedActivityActiveDTO> pageDTO = new PageDTO<>();
        pageDTO.setTotal((int) resultPage.getTotal());

        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            List<RedActivityActiveDTO> resultList = resultPage.getRecords().stream().map(item -> {
                RedActivityActiveDTO dto = new RedActivityActiveDTO();
                BeanUtils.copyProperties(item, dto);

                // 填充URL
                return fillUrl(dto);
            }).collect(Collectors.toList());

            pageDTO.setList(resultList);
        }

        return Result.ok(pageDTO);
    }

    @Override
    public Result<List<String>> getProductTypes() {
        try {
            List<String> productTypes = redActivityActiveMapper.selectDistinctProductTypes();
            return Result.ok(productTypes);
        } catch (Exception e) {
            log.error("查询作品类型失败", e);
            return Result.fail("查询作品类型失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<RedActivityJudgeDTO>> getJudgeListByMobile(String mobile) {
        LambdaQueryWrapper<RedActivityJudge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RedActivityJudge::getJudgePhone, mobile);
        queryWrapper.orderByAsc(RedActivityJudge::getId);
        List<RedActivityJudge> list = redActivityJudgeMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmptyy(list)) {
            return Result.ok(list.stream().map(item -> {
                RedActivityJudgeDTO dto = new RedActivityJudgeDTO();
                BeanUtils.copyProperties(item, dto);
                return dto;
            }).collect(Collectors.toList()));
        }
        return Result.ok(Collections.emptyList());
    }

    @Override
    public Result<Boolean> auditing(RedActivityAuditingParam param) {
        // 查询评委赛道
        RedActivityJudge redActivityJude = getJudge(param.getJudgeMobile(), "终评");
        if (redActivityJude == null) {
            return Result.fail("该账号不是终审评委");
        }

        LambdaUpdateWrapper<RedActivityActive> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RedActivityActive::getId, param.getProductId());
        updateWrapper.set(RedActivityActive::getReState, param.getState());
        updateWrapper.set(RedActivityActive::getUpdateTime, new Date());
        redActivityActiveMapper.update(null, updateWrapper);
        return Result.ok(true);
    }
}
