package com.jxntv.gvideo.media.api.domain.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("red_activity_judge_score")
public class RedActivityJudgeScore {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动ID
     */
    @TableField("activity_id")
    private Long activityId;

    @TableField("judge_id")
    private Long judgeId;

    @TableField("product_id")
    private Long productId;

    @TableField("state")
    private Integer state;

    @TableField("score")
    private Integer score;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
