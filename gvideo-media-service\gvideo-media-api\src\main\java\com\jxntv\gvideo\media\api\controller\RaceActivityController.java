package com.jxntv.gvideo.media.api.controller;

import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.service.RaceActivityService;
import com.jxntv.gvideo.media.api.service.RaceLiveBroadcastService;
import com.jxntv.gvideo.media.api.service.RaceScheduleService;
import com.jxntv.gvideo.media.api.service.RaceTeamService;
import com.jxntv.gvideo.media.client.RaceActivityClient;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceActivitySearchDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleSearchDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamSearchDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 球赛活动控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(value = "球赛活动相关接口", tags = {"球赛活动相关接口"})
public class RaceActivityController implements RaceActivityClient {

    @Resource
    private RaceActivityService raceActivityService;

    @Resource
    private RaceTeamService raceTeamService;

    @Resource
    private RaceScheduleService raceScheduleService;

    @Resource
    private RaceLiveBroadcastService raceLiveBroadcastService;

    @Override
    public Result<Boolean> addActivity(RaceActivityDTO dto) {
        return Result.ok(raceActivityService.addActivity(dto));
    }

    @Override
    public Result<Boolean> updateActivity(RaceActivityDTO dto) {
        return Result.ok(raceActivityService.updateActivity(dto));
    }

    @Override
    public Result<PageDTO<RaceActivityDTO>> page(RaceActivitySearchDTO searchDTO) {
        return Result.ok(raceActivityService.page(searchDTO));
    }

    @Override
    public Result<RaceActivityDTO> getActivityById(Long id) {
        return Result.ok(raceActivityService.getActivityById(id));
    }

    @Override
    public Result<Boolean> addTeam(RaceTeamDTO dto) {
        return Result.ok(raceTeamService.addTeam(dto));
    }

    @Override
    public Result<Boolean> updateTeam(RaceTeamDTO dto) {
        return Result.ok(raceTeamService.updateTeam(dto));
    }

    @Override
    public Result<PageDTO<RaceTeamDTO>> teamPage(RaceTeamSearchDTO searchDTO) {
        return Result.ok(raceTeamService.page(searchDTO));
    }

    @Override
    public Result<RaceTeamDTO> getTeamById(Long id) {
        return Result.ok(raceTeamService.getTeamById(id));
    }

    @Override
    public Result<Boolean> addSchedule(RaceScheduleDTO dto) {
        return Result.ok(raceScheduleService.addSchedule(dto));
    }

    @Override
    public Result<Boolean> updateSchedule(RaceScheduleDTO dto) {
        return Result.ok(raceScheduleService.updateSchedule(dto));
    }

    @Override
    public Result<PageDTO<RaceScheduleDTO>> schedulePage(RaceScheduleSearchDTO searchDTO) {
        return Result.ok(raceScheduleService.page(searchDTO));
    }


    @Override
    public Result<RaceScheduleDTO> getScheduleById(Long id) {
        return Result.ok(raceScheduleService.getScheduleById(id));
    }

    @Override
    public Result<Boolean> deleteSchedule(Long id) {
        return Result.ok(raceScheduleService.deleteSchedule(id));
    }

    @Override
    public Result<Boolean> addLiveBroadcast(RaceLiveBroadcastDTO dto) {
        return Result.ok(raceLiveBroadcastService.addLiveBroadcast(dto));
    }

    @Override
    public Result<Boolean> updateLiveBroadcast(RaceLiveBroadcastDTO dto) {
        return Result.ok(raceLiveBroadcastService.updateLiveBroadcast(dto));
    }

    @Override
    public Result<PageDTO<RaceLiveBroadcastDTO>> liveBroadcastPage(RaceLiveBroadcastSearchDTO searchDTO) {
        return Result.ok(raceLiveBroadcastService.page(searchDTO));
    }

    @Override
    public Result<RaceLiveBroadcastDTO> getLiveBroadcastById(Long id) {
        return Result.ok(raceLiveBroadcastService.getLiveBroadcastById(id));
    }

    @Override
    public Result<Boolean> deleteLiveBroadcast(Long id) {
        return Result.ok(raceLiveBroadcastService.deleteLiveBroadcast(id));
    }

    @Override
    public Result<RaceLiveBroadcastDTO> getLatestLiveBroadcastByActivityId(Long activityId) {
        return Result.ok(raceLiveBroadcastService.getLatestLiveBroadcastByActivityId(activityId));
    }
}
