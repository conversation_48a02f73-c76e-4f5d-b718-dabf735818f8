package com.jxntv.gvideo.search.api.converter;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import com.jxntv.gvideo.search.api.domain.MediaResourceIndex;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexDTO;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/7
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class MediaResourceIndexConverter {

    public MediaResourceIndexDTO convert(MediaResourceIndex entity){
        MediaResourceIndexDTO mediaResourceIndexDTO = new MediaResourceIndexDTO();
        mediaResourceIndexDTO.setId(entity.getId());
        mediaResourceIndexDTO.setTitle(entity.getTitle());
        mediaResourceIndexDTO.setDescription(entity.getDescription());
        mediaResourceIndexDTO.setContentId(entity.getContentId());
        mediaResourceIndexDTO.setVideoUrl(entity.getVideoUrl());
        mediaResourceIndexDTO.setThumb(entity.getThumb());
        mediaResourceIndexDTO.setStatus(entity.getStatus());
        mediaResourceIndexDTO.setCreateTime(entity.getCreateTime());

        if (Objects.nonNull(entity.getLabels())) {
            mediaResourceIndexDTO.setLabels(entity.getLabels().stream().map(this::convert).collect(Collectors.toList()));
        }

        if (Objects.nonNull(entity.getCategories())) {
            mediaResourceIndexDTO.setCategories(entity.getCategories().stream().map(this::convert).collect(Collectors.toList()));
        }


        return mediaResourceIndexDTO;

    }


    public MediaResourceIndexDTO.LabelDTO convert(MediaResourceIndex.Label entity){
        MediaResourceIndexDTO.LabelDTO label = new MediaResourceIndexDTO.LabelDTO();
        label.setType(entity.getType());
        label.setLabelId(entity.getLabelId());
        label.setLabelName(entity.getLabelName());
        return label;
    }


    public MediaResourceIndexDTO.CategoryDTO convert(MediaResourceIndex.Category entity){
        MediaResourceIndexDTO.CategoryDTO categoryDTO = new MediaResourceIndexDTO.CategoryDTO();
        categoryDTO.setCategoryId(entity.getCategoryId());
        categoryDTO.setCategoryName(entity.getCategoryName());
        return categoryDTO;

    }

}
