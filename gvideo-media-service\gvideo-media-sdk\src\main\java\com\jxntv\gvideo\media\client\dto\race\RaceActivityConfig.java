package com.jxntv.gvideo.media.client.dto.race;

import java.io.Serializable;
import lombok.Data;

/**
 * 比赛活动配置DTO
 *
 * <AUTHOR>
 */
@Data
public class RaceActivityConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 积分榜ossid
     */
    private String scoreOssId;

    /**
     * 最小初始助威数
     */
    private Integer minInitialCheer;


    /**
     * 最大初始助威数
     */
    private Integer maxInitialCheer;

    /**
     * 每日最大助威次数
     */
    private Integer maxCheerTimes;

}
