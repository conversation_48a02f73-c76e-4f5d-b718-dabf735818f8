package com.jxntv.gvideo.search.api.service;

import com.jxntv.gvideo.search.api.domain.MediaResourceIndex;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexSearchDTO;

import java.util.List;

import org.springframework.data.domain.Page;

public interface MediaResourceIndexService extends ElasticsearchService<MediaResourceIndex> {

    Page<MediaResourceIndex> search(MediaResourceIndexSearchDTO searchDTO);

    /**
     * 批量索引
     *
     * @param ids 索引ID列表
     * @return 是否处理成功
     */
    boolean batchIndexByIds(List<Long> ids);

}