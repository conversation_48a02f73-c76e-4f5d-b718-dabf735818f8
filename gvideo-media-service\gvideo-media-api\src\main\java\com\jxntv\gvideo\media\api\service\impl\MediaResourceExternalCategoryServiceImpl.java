package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceExternalCategory;
import com.jxntv.gvideo.media.api.repository.MediaResourceExternalCategoryMapper;
import com.jxntv.gvideo.media.api.service.MediaResourceExternalCategoryService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MediaResourceExternalCategoryServiceImpl extends ServiceImpl<MediaResourceExternalCategoryMapper, MediaResourceExternalCategory> implements MediaResourceExternalCategoryService {@Override
    public List<MediaResourceExternalCategory> listByMediaId(Long mediaId) {
        LambdaQueryWrapper<MediaResourceExternalCategory> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MediaResourceExternalCategory::getMediaId, mediaId);
        return this.list(queryWrapper);
    }

@Override
public List<MediaResourceExternalCategory> listByMediaIds(List<Long> mediaIds) {
    LambdaQueryWrapper<MediaResourceExternalCategory> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper.in(MediaResourceExternalCategory::getMediaId, mediaIds);
    return this.list(queryWrapper);
}
}
