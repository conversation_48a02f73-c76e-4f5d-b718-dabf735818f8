package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.race.*;
import com.jxntv.gvideo.media.client.fallback.RaceActivityClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 球赛活动相关客户端接口 包含活动信息管理、队伍管理、赛程管理及预约管理功能
 *
 * <AUTHOR>
 */
@FeignClient(name = "media-service", contextId = "race-activity", fallbackFactory = RaceActivityClientFallback.class)
public interface RaceActivityClient {

    String PREFIX = "/api/media/race/activity";

    /**
     * 分页查询活动列表
     *
     * @param searchDTO 查询条件
     * @return 活动分页数据
     */
    @PostMapping(PREFIX + "/page")
    Result<PageDTO<RaceActivityDTO>> page(@RequestBody RaceActivitySearchDTO searchDTO);

    /**
     * 新增活动信息
     *
     * @param dto 活动信息
     * @return 处理结果
     */
    @PostMapping(PREFIX)
    Result<Boolean> addActivity(@RequestBody RaceActivityDTO dto);

    /**
     * 修改活动信息
     *
     * @param dto 活动信息
     * @return 处理结果
     */
    @PutMapping(PREFIX)
    Result<Boolean> updateActivity(@RequestBody RaceActivityDTO dto);

    /**
     * 活动详情
     *
     * @param id 活动ID
     * @return 活动详情信息
     */
    @GetMapping(PREFIX + "/info/{id}")
    Result<RaceActivityDTO> getActivityById(@PathVariable Long id);

    /**
     * 新增队伍信息
     *
     * @param dto 队伍信息
     * @return 处理结果
     */
    @PostMapping(PREFIX + "/team")
    Result<Boolean> addTeam(@RequestBody RaceTeamDTO dto);

    /**
     * 修改队伍信息
     *
     * @param dto 队伍信息
     * @return 处理结果
     */
    @PutMapping(PREFIX + "/team")
    Result<Boolean> updateTeam(@RequestBody RaceTeamDTO dto);

    /**
     * 分页查询队伍列表
     *
     * @param searchDTO 查询条件
     * @return 队伍分页数据
     */
    @PostMapping(PREFIX + "/team/page")
    Result<PageDTO<RaceTeamDTO>> teamPage(@RequestBody RaceTeamSearchDTO searchDTO);

    /**
     * 队伍详情
     *
     * @param id 队伍ID
     * @return 队伍详情信息
     */
    @GetMapping(PREFIX + "/team/info/{id}")
    Result<RaceTeamDTO> getTeamById(@PathVariable Long id);

    /**
     * 新增赛程信息
     *
     * @param dto 赛程信息
     * @return 处理结果
     */
    @PostMapping(PREFIX + "/schedule")
    Result<Boolean> addSchedule(@RequestBody RaceScheduleDTO dto);

    /**
     * 修改赛程信息
     *
     * @param dto 赛程信息
     * @return 处理结果
     */
    @PutMapping(PREFIX + "/schedule")
    Result<Boolean> updateSchedule(@RequestBody RaceScheduleDTO dto);

    /**
     * 分页查询赛程列表
     *
     * @param searchDTO 查询条件
     * @return 赛程分页数据
     */
    @PostMapping(PREFIX + "/schedule/page")
    Result<PageDTO<RaceScheduleDTO>> schedulePage(@RequestBody RaceScheduleSearchDTO searchDTO);

    /**
     * 赛程详情
     *
     * @param id 赛程ID
     * @return 赛程详情信息
     */
    @GetMapping(PREFIX + "/schedule/info/{id}")
    Result<RaceScheduleDTO> getScheduleById(@PathVariable Long id);

    /**
     * 删除赛程
     *
     * @param id 赛程ID
     * @return 删除结果
     */
    @DeleteMapping(PREFIX + "/schedule/{id}")
    Result<Boolean> deleteSchedule(@PathVariable Long id);

    /**
     * 新增比赛直播信息
     *
     * @param dto 比赛直播信息
     * @return 处理结果
     */
    @PostMapping(PREFIX + "/live/broadcast")
    Result<Boolean> addLiveBroadcast(@RequestBody RaceLiveBroadcastDTO dto);

    /**
     * 修改比赛直播信息
     *
     * @param dto 比赛直播信息
     * @return 处理结果
     */
    @PutMapping(PREFIX + "/live/broadcast")
    Result<Boolean> updateLiveBroadcast(@RequestBody RaceLiveBroadcastDTO dto);

    /**
     * 分页查询比赛直播列表
     *
     * @param searchDTO 查询条件
     * @return 比赛直播分页数据
     */
    @PostMapping(PREFIX + "/live/broadcast/page")
    Result<PageDTO<RaceLiveBroadcastDTO>> liveBroadcastPage(@RequestBody RaceLiveBroadcastSearchDTO searchDTO);

    /**
     * 比赛直播详情
     *
     * @param id 比赛直播ID
     * @return 比赛直播详情信息
     */
    @GetMapping(PREFIX + "/live/broadcast/info/{id}")
    Result<RaceLiveBroadcastDTO> getLiveBroadcastById(@PathVariable Long id);

    /**
     * 删除比赛直播
     *
     * @param id 比赛直播ID
     * @return 删除结果
     */
    @DeleteMapping(PREFIX + "/live/broadcast/{id}")
    Result<Boolean> deleteLiveBroadcast(@PathVariable Long id);

    /**
     * 查询最新活动直播信息
     * 当前时间在直播时间范围内且取最大ID
     *
     * @param activityId 活动ID
     * @return 最新的活动直播信息
     */
    @GetMapping(PREFIX + "/live/broadcast/latest/{activityId}")
    Result<RaceLiveBroadcastDTO> getLatestLiveBroadcastByActivityId(@PathVariable Long activityId);

}