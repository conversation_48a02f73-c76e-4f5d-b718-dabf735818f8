package com.jxntv.gvideo.search.api.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.MediaResourceExternalClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalCategoryDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceLabelDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceSearchDTO;
import com.jxntv.gvideo.media.client.enums.OrderBy;
import com.jxntv.gvideo.search.api.domain.MediaResourceIndex;
import com.jxntv.gvideo.search.api.repository.MediaResourceIndexRepository;
import com.jxntv.gvideo.search.api.service.MediaResourceIndexService;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexSearchDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MediaResourceIndexServiceImpl extends ElasticsearchServiceImpl<MediaResourceIndex>
        implements MediaResourceIndexService {

    @Resource
    private MediaResourceClient mediaResourceClient;
    @Resource
    private MediaResourceExternalClient mediaResourceExternalClient;
    @Resource
    private MediaResourceIndexRepository mediaResourceIndexRepository;
    @Resource
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void init() {
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.submit(() -> {

            int batchSize = 200;
            int total = 0;
            Long endId = Long.MAX_VALUE;

            while (true) {

                long startTime = System.currentTimeMillis();
                MediaResourceSearchDTO searchDTO = new MediaResourceSearchDTO();
                searchDTO.setCurrent(1);
                searchDTO.setSize(batchSize);
                searchDTO.setEndId(endId);
                searchDTO.setOrderBy(OrderBy.ID.name());
                searchDTO.setAsc(false);

                PageDTO<MediaResourceDTO> page = mediaResourceClient.page(searchDTO).orElse(null);
                if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getList())) {
                    break;
                }

                // 批量索引
                List<MediaResourceDTO> list = page.getList();
                try {
                    this.batchIndexByIds(list.stream().map(e -> e.getId()).collect(Collectors.toList()));
                } catch (Exception e) {
                    log.error("media-resource 索引同步异常,list->{}", JSON.toJSONString(list), e);
                }

                total += list.size();
                endId = list.get(list.size() - 1).getId();
                log.info("media-resource 索引同步:当前endId={}，预计剩余时间:{}s", endId,
                        (System.currentTimeMillis() - startTime) * (page.getTotal() - total) / batchSize / 1000);
            }
        });

    }

    @Override
    public Page<MediaResourceIndex> search(MediaResourceIndexSearchDTO dto) {
        try {
            SearchRequest originalSearchRequest = buildSearchRequest(dto);
            SearchResponse originalResponse = restHighLevelClient.search(originalSearchRequest, RequestOptions.DEFAULT);
            log.info("资源索引搜索结果:{}", JSON.toJSONString(originalResponse));

            List<MediaResourceIndex> originalContents = processSearchResult(originalResponse);

            return new PageImpl<>(
                    originalContents,
                    PageRequest.of(dto.getCurrent() - 1, dto.getSize()),
                    originalResponse.getHits().getTotalHits().value);
        } catch (Exception e) {
            log.error("MediaResource索引查询错误", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建搜索请求
     */
    private SearchRequest buildSearchRequest(MediaResourceIndexSearchDTO dto) {
        SearchRequest searchRequest = new SearchRequest("media_resource_index_20250715");
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 设置查询参数
        sourceBuilder.query(buildCompositeQuery(dto));
        // 设置排序参数
        List<SearchDTO.Sort> sorts = dto.getSorts();
        if (Objects.nonNull(sorts)) {
            for (SearchDTO.Sort sort : sorts) {
                sourceBuilder.sort(sort.getOrderBy(), sort.isAsc() ? SortOrder.ASC : SortOrder.DESC);
            }
        } else {
            // 默认按照时间倒序
            sourceBuilder.sort("createTime", SortOrder.DESC);
        }

        // 设置分页参数
        sourceBuilder.from((dto.getCurrent() - 1) * dto.getSize());
        sourceBuilder.size(dto.getSize());

        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    /**
     * 构建数据查询
     */
    private QueryBuilder buildCompositeQuery(MediaResourceIndexSearchDTO dto) {

        BoolQueryBuilder mainQuery = QueryBuilders.boolQuery();

        // 1、状态过滤
        if (Objects.nonNull(dto.getStatus())) {
            QueryBuilder statusQuery = QueryBuilders.termsQuery("status", dto.getStatus());
            mainQuery.filter(statusQuery);
        }

        // 2、时间过滤
        if (Objects.nonNull(dto.getStartTime()) || Objects.nonNull(dto.getEndTime())) {
            RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("createTime");
            if (Objects.nonNull(dto.getStartTime())) {
                timeRangeQuery.gte(dto.getStartTime());
            }
            if (Objects.nonNull(dto.getEndTime())) {
                timeRangeQuery.lte(dto.getEndTime());
            }

            mainQuery.filter(timeRangeQuery);
        }

        // 3、标签过滤
        if (!CollectionUtils.isEmpty(dto.getLabelFilters())) {

            dto.getLabelFilters().forEach(labelFilter -> {

                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                if (Objects.nonNull(labelFilter.getId())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("labels.labelId", labelFilter.getId()));
                }
                if (Objects.nonNull(labelFilter.getType())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("labels.type", labelFilter.getType()));
                }
                if (Objects.nonNull(labelFilter.getName())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("labels.labelName", labelFilter.getName()));
                }

                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("labels", boolQueryBuilder, ScoreMode.None);
                mainQuery.filter(nestedQuery);
            });

        }

        // 4. 分类名称过滤
        if (!CollectionUtils.isEmpty(dto.getCategoryNames())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("categories.categoryName",
                    dto.getCategoryNames());
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("categories", termsQueryBuilder, ScoreMode.None);
            mainQuery.filter(nestedQuery);
        }

        // 5. 分类ID过滤
        if (!CollectionUtils.isEmpty(dto.getCategoryIds())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("categories.categoryId",
                    dto.getCategoryIds());
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("categories", termsQueryBuilder, ScoreMode.None);
            mainQuery.filter(nestedQuery);
        }

        return mainQuery;
    }

    private List<MediaResourceIndex> processSearchResult(SearchResponse response) {
        return Arrays.stream(response.getHits().getHits())
                .map(hit -> JSON.parseObject(hit.getSourceAsString(), MediaResourceIndex.class))
                .collect(Collectors.toList());
    }

    @Override
    public boolean batchIndexByIds(List<Long> ids) {

        List<MediaResourceDTO> dtoList = mediaResourceClient.baseInfoBulk(ids).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(dtoList)) {
            return false;
        }

        // 1. 批量查询外部资源
        List<MediaResourceExternalDTO> externalList = mediaResourceExternalClient.batchGetByMediaIds(ids)
                .orElse(Collections.emptyList());
        Map<Long, MediaResourceExternalDTO> externalMap = externalList.stream()
                .collect(Collectors.toMap(MediaResourceExternalDTO::getMediaId, Function.identity()));

        // 2. 批量查询标签信息
        List<MediaResourceLabelDTO> labelList = mediaResourceClient.batchGetLabelsByIds(ids)
                .orElse(Collections.emptyList());
        Map<Long, List<MediaResourceLabelDTO>> labelMap = labelList.stream()
                .collect(Collectors.groupingBy(MediaResourceLabelDTO::getResourceId));

        // 3. 批量查询分类信息
        List<MediaResourceExternalCategoryDTO> categoryList = mediaResourceExternalClient
                .batchGetCategoryByMediaIds(ids).orElse(Collections.emptyList());
        Map<Long, List<MediaResourceExternalCategoryDTO>> categoryMap = categoryList.stream()
                .collect(Collectors.groupingBy(MediaResourceExternalCategoryDTO::getMediaId));

        // 4.批量构建索引

        List<MediaResourceIndex> indexList = dtoList.stream().map(dto -> {
            MediaResourceIndex index = new MediaResourceIndex();
            index.setId(String.valueOf(dto.getId()));
            index.setTitle(dto.getShowName());
            index.setDescription(dto.getIntroduction());
            index.setStatus(dto.getStatus());
            index.setCreateTime(dto.getCreateDate().getTime());

            // 封面和视频URL
            MediaResourceExternalDTO externalDTO = externalMap.get(dto.getId());
            if (Objects.nonNull(externalDTO)) {
                index.setContentId(externalDTO.getContentId());
                index.setVideoUrl(externalDTO.getUrl());
                index.setThumb(externalDTO.getCover());
            }

            // 标签
            List<MediaResourceLabelDTO> labels = labelMap.get(dto.getId());
            if (!CollectionUtils.isEmpty(labels)) {
                List<MediaResourceIndex.Label> list = labels.stream().map(l -> {
                    MediaResourceIndex.Label label = new MediaResourceIndex.Label();
                    label.setLabelName(l.getLabelName());
                    label.setLabelId(l.getLabelId());
                    label.setType(l.getType());
                    return label;
                }).collect(Collectors.toList());
                index.setLabels(list);
            }

            // 分类
            List<MediaResourceExternalCategoryDTO> categories = categoryMap.get(dto.getId());
            if (!CollectionUtils.isEmpty(categories)) {
                List<MediaResourceIndex.Category> list = categories.stream().map(c -> {
                    MediaResourceIndex.Category category = new MediaResourceIndex.Category();
                    category.setCategoryId(c.getCategoryId());
                    category.setCategoryName(c.getCategoryName());
                    return category;
                }).collect(Collectors.toList());

                index.setCategories(list);
            }

            return index;
        }).collect(Collectors.toList());

        mediaResourceIndexRepository.saveAll(indexList);

        return true;

    }

}