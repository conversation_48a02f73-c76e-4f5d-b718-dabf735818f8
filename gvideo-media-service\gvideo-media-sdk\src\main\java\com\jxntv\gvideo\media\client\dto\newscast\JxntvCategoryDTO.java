package com.jxntv.gvideo.media.client.dto.newscast;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class JxntvCategoryDTO implements Serializable {
    @JsonProperty("contentid")
    private Long contentid;
    @JsonProperty("catid")
    private String catid;
    @JsonProperty("appid")
    private Integer appid;
    @JsonProperty("title")
    private String title;
    @JsonProperty("description")
    private String description;
    @JsonProperty("tags")
    private String tags;
    @JsonProperty("time")
    private Long time;
    @JsonProperty("appName")
    private String appName;
    @JsonProperty("url")
    private String url;
    @JsonProperty("thumb")
    private String thumb;
    @JsonProperty("thumb_ratio")
    private Object thumbRatio;
    @JsonProperty("published")
    private String published;
    @JsonProperty("jspid")
    private Integer jspid;
    @JsonProperty("id")
    private Integer id;

}
