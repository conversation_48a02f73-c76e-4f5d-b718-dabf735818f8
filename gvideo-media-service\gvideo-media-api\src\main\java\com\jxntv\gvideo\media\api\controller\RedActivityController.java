package com.jxntv.gvideo.media.api.controller;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.RestController;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.event.RedActivityProductScoreEvent;
import com.jxntv.gvideo.media.api.service.RedActivityService;
import com.jxntv.gvideo.media.client.RedActivityClient;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityActiveDTO;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityAuditingParam;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityDTO;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityJudgeDTO;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityQueryParam;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityScoringParam;

@RestController
public class RedActivityController implements RedActivityClient {
    @Resource
    private RedActivityService redActivityService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public Result<String> sendCode(String mobile) {
        return redActivityService.sendCode(mobile);
    }

    @Override
    public Result<String> register(RedActivityActiveDTO dto) {
        return redActivityService.register(dto);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listProject(RedActivityQueryParam queryParam) {
        return redActivityService.listProject(queryParam);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listFinalProject(RedActivityQueryParam queryParam) {
        return redActivityService.listFinalProject(queryParam);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listProduct(RedActivityQueryParam queryParam) {
        return redActivityService.listProduct(queryParam);
    }

    @Override
    public Result<List<RedActivityActiveDTO>> query(RedActivityQueryParam queryParam) {
        return redActivityService.query(queryParam);
    }

    @Override
    public Result<String> scoring(RedActivityScoringParam param) {
        Result<String> result = redActivityService.scoring(param);
        if (result.callSuccess()) {
            RedActivityProductScoreEvent event = new RedActivityProductScoreEvent();
            event.setProductId(param.getProductId());
            applicationEventPublisher.publishEvent(event);
        }
        return result;
    }

    @Override
    public Result<String> getJudgeTracks(String mobile, String type) {
        return redActivityService.getJudgeTracks(mobile, type);
    }

    @Override
    public Result<String> submit(RedActivityScoringParam param) {
        return redActivityService.submit(param);
    }

    @Override
    public Result<RedActivityDTO> getDetailById(Long activityId) {
        return redActivityService.getDetailById(activityId);
    }

    @Override
    public Result<PageDTO<RedActivityActiveDTO>> listScoredProducts(RedActivityQueryParam queryParam) {
        return redActivityService.listScoredProducts(queryParam);
    }

    @Override
    public Result<List<String>> getProductTypes() {
        return redActivityService.getProductTypes();
    }

    @Override
    public Result<List<RedActivityJudgeDTO>> getJudgeListByMobile(String mobile) {
        return redActivityService.getJudgeListByMobile(mobile);
    }

    @Override
    public Result<Boolean> auditing(RedActivityAuditingParam param) {
        return redActivityService.auditing(param);
    }
}
