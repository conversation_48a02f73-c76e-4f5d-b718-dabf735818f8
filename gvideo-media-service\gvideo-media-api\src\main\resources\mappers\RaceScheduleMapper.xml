<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxntv.gvideo.media.api.repository.RaceScheduleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxntv.gvideo.media.api.domain.entity.RaceSchedule">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="title" property="title" />
        <result column="introduction" property="introduction" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="home_team_id" property="homeTeamId" />
        <result column="away_team_id" property="awayTeamId" />
        <result column="home_team_score" property="homeTeamScore" />
        <result column="away_team_score" property="awayTeamScore" />
        <result column="home_team_cheer" property="homeTeamCheer" />
        <result column="away_team_cheer" property="awayTeamCheer" />
        <result column="resource_id" property="resourceId" />
        <result column="push_title" property="pushTitle" />
        <result column="push_content" property="pushContent" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, title, introduction, start_time, end_time, home_team_id, away_team_id,
        home_team_score, away_team_score, home_team_cheer, away_team_cheer, resource_id, push_title, push_content, status,
        del_flag, create_date, update_date
    </sql>

    <!-- 查询设备预约的赛程列表 -->
    <select id="selectUserSubscribeList" resultMap="BaseResultMap">
        SELECT
        rs.<include refid="Base_Column_List" />
        FROM race_schedule rs
        INNER JOIN race_schedule_subscribe rss ON rs.id = rss.schedule_id
        WHERE rs.activity_id = #{activityId}
        AND rs.del_flag = 0
        AND rss.device = #{deviceId}
        AND rss.del_flag = 0
        ORDER BY rs.start_time ASC
    </select>

</mapper>