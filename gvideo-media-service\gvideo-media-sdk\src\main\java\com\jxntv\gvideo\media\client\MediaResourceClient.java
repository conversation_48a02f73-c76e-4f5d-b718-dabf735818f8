package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.dto.search.BroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.LiveBroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceSearchDTO;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MediaResourceClient {
    String FEED_VIDEO_LIST_KEY = "feed_video_list_key:";
    String FEED_VIDEO_ADVERT_LIST_KEY = "feed_video_advert_list_key:";

    @GetMapping("/api/media-resources/feed")
    Result<List<MediaResourceDTO>> feed(@RequestParam(required = false) String cursor, @RequestParam(required = false) Long tabId);

    @GetMapping("/api/media-resource/feed-release-video/cache")
    Result<Map<String, List<Long>>> feedReleaseVideo();

    @GetMapping("/api/media-resource/feed-release-video")
    Result<List<Long>> feedReleaseVideo(@RequestParam(name = "device") String device);

    @GetMapping("/api/media-resource/feed-video/cache")
    Result<List<Long>> feedVideo(@RequestParam("maxRank") Integer maxRank);

    @GetMapping("/api/media-resource/feed-video")
    Result<PageDTO<Long>> feedVideo(@RequestParam(name = "pageNum") Integer pageNum, @RequestParam(name = "device") String device);

    @PostMapping("/api/media-resources/page")
    Result<PageDTO<MediaResourceDTO>> page(@RequestBody MediaResourceSearchDTO searchDTO);

    @PostMapping("/api/media-resources/group-content/page")
    Result<PageDTO<MediaResourceDTO>> listResourcePage(@RequestBody MediaResourceSearchDTO searchDTO);

    @PostMapping("/api/media-resources/author-content/page")
    Result<PageDTO<MediaResourceDTO>> listAuthorContentPage(@RequestBody MediaResourceSearchDTO searchDTO);

    @GetMapping("/api/media-resources/{id}")
    Result<MediaResourceDTO> get(@PathVariable Long id);

    @GetMapping("/api/media-resources/{id}/images")
    Result<List<MediaResourceImageDTO>> getImages(@PathVariable Long id);

    @GetMapping("/api/media-resources/{id}/location")
    Result<MediaResourceLocationDTO> getLocation(@PathVariable Long id);

    @GetMapping("/api/media-resources/{id}/labels")
    Result<List<MediaResourceLabelDTO>> getLabels(@PathVariable Long id);

    @PostMapping("/api/media-resources/labels/batch")
    Result<List<MediaResourceLabelDTO>> batchGetLabelsByIds(@RequestBody List<Long> ids);


    @PostMapping("/api/media-resources/bulk")
    Result<List<MediaResourceDTO>> bulk(@RequestBody List<Long> ids);

    @PostMapping("/api/media-resources/base-info/bulk")
    Result<List<MediaResourceDTO>> baseInfoBulk(@RequestBody List<Long> ids);

    @PostMapping("/api/media-resources")
    Result<Long> create(@RequestBody MediaResourceDTO mediaResourceDTO);

    @PostMapping("/api/media-resources-ugc")
    Result<Long> createUgc(@RequestBody MediaUgcFileResourceDTO mediaUgcFileResourceDTO);

    @PutMapping("/api/media-ugc-resource/update/ugc")
    Result<Void> updateUgc(@RequestBody MediaUgcFileResourceDTO mediaFileResourceDTO);

    @GetMapping("/api/media-resources-ugc/count/{releaseId}")
    Result<Integer> ugcWorksCount(@PathVariable long releaseId, @RequestParam Boolean isIncludePrivacy);

    @PutMapping("/api/media-resources/{id}")
    Result update(@PathVariable Long id, @RequestBody MediaResourceDTO mediaResourceDTO);

    @PutMapping("/api/media-resources/{id}/simple")
    Result updateSimple(@PathVariable Long id, @RequestBody MediaResourceDTO mediaResourceDTO);

    @PutMapping("/api/media-resources/{id}/status")
    Result updateAuditStatus(@PathVariable Long id, @RequestBody MediaResourceDTO mediaResourceDTO);

    @GetMapping("/api/media-resources/{id}/pendant")
    Result<MediaResourcePendantDTO> getPendant(@PathVariable Long id);

    @GetMapping("/api/media-resources/batch/pendant")
    Result<List<MediaResourcePendantDTO>> listPendantByIds(@RequestParam List<Long> mediaIds);

    @PostMapping("/api/media-resources/{id}/pendant")
    Result savePendant(@PathVariable Long id, @RequestBody MediaResourcePendantDTO dto);

    @DeleteMapping("/api/media-resources/pendant")
    Result removeMediaPendant(@RequestBody List<Long> mediaIds);

    @GetMapping("/api/media-resources/fuzzy")
    Result<List<MediaResourceDTO>> fuzzy(@RequestParam List<Integer> contentTypeList, @RequestParam String internalName);

    @GetMapping("/api/media-resources/fuzzy/pptv")
    Result<List<MediaResourceDTO>> fuzzyPptv(@RequestParam List<Integer> contentTypeList, @RequestParam String showName);

    @GetMapping("/api/media-resources/sys-configs")
    Result<List<SysConfigDTO>> configs();

    @GetMapping("/api/media-resources/countByCertificationId")
    Result<Integer> countByCertificationId(@RequestParam Long certificationId);

    @GetMapping("/api/media-resources/certificationId/media")
    Result<PageDTO<MediaResourceDTO>> getOwnMedias(@RequestParam long authorId, @RequestParam int current, @RequestParam int size);

    @GetMapping("/api/media-resources/countByColumnId")
    Result<Integer> countByColumnId(@RequestParam Long columnId);

    @PutMapping("/api/media-resources/batch/discard")
    Result batchDiscard(@RequestBody List<Long> mediaIds);

    @PutMapping("/api/media-resources/discard/ugc/{ugcId}")
    Result<Void> discardUgc(@PathVariable("ugcId") Long ugcId);


    @PutMapping("/api/media-resources/sync/status/{id}")
    Result<Void> statusModify(@PathVariable Long id, @RequestParam Integer status);

    @DeleteMapping("/api/media-resources/{id}")
    Result<Void> deleteById(@PathVariable Long id);

    @GetMapping("/api/media-resources/mediacloud")
    Result<List<MediaCloudResourceDTO>> getMediaCloudResourceList(@RequestParam("mediaCloudIds") String mediaCloudIds);

    /**
     * 名称联想分页接口
     *
     * @param key
     * @param current
     * @param size
     * @param tenantId
     * @return
     */
    @GetMapping("/api/media-resources/fuzzy/page")
    Result<PageDTO<MediaResourceDTO>> fuzzyPage(@RequestParam String key, @RequestParam List<Integer> contentTypes, @RequestParam String excludeType, @RequestParam Integer current, @RequestParam Integer size, @RequestParam Long tenantId, @RequestParam(required = false) Integer releaseType);

    /**
     * 统计c端用户发布资源数量
     *
     * @param jid
     * @return
     */
    @GetMapping("/api/media-resource/count/by/jid")
    Result<Integer> countByJid(@RequestParam Long jid);


    /**
     * 圈子话题统计启用的内容数量
     *
     * @param mediaLst
     * @return
     */
    @PostMapping("/api/media-resource/count/for/group/topic")
    Result<Integer> countEnable(@RequestBody List<Long> mediaLst);

    /**
     * 通过作者id查询
     *
     * @param jidLst
     * @return
     */
    @PostMapping("/api/media-resource/get/release-id")
    Result<List<Long>> getByCondition(@RequestBody List<Long> jidLst, @RequestParam(required = false) Date start, @RequestParam(required = false) Date end);

    /**
     * 编辑(新增、更新)新闻稿件资源（来自于赣云的文章、拆条、节目资源）
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/media-resource/edit/news")
    Result<Long> editNews(@RequestBody MediaResourceDTO dto);

    /**
     * 查询新闻稿件新闻稿件资源
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/media-resource/query/news")
    Result<PageDTO<MediaResourceDTO>> queryNews(@RequestBody NewsQueryParam dto);

    /**
     * 筛选包含直播的内容
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/media-resource/ids/content-type")
    Result<List<MediaResourceDTO>> findByIdsAndContentType(@RequestBody FindByIdsAndContentTypeDTO dto);

    /**
     * 审核通过时修改通过时间
     *
     * @param mediaId
     * @return
     */
    @PutMapping("/api/media-resource/by-audit/update/pass-date")
    Result<Void> updateLastPassDate(@RequestParam Long mediaId);


    /**
     * 查询用户动态列表
     *
     * @param searchDTO 作者id
     * @return
     */
    @PostMapping("/api/media-resource/posts/page")
    Result<PageDTO<MediaResourceDTO>> pagePosts(@RequestBody PostSearchDTO searchDTO);

    /**
     * 查询青少年用户动态列表
     *
     * @param searchDTO 作者id
     * @return
     */
    @PostMapping("/api/media-resource/posts/junior/page")
    Result<PageDTO<MediaResourceDTO>> pageJuniorPosts(@RequestBody PostSearchDTO searchDTO);


    @PostMapping("/api/media-resource/follow/list")
    Result<PageDTO<MediaResourceDTO>> followList(@RequestParam Long jid, @RequestParam Integer pageNum);

    /**
     * 查询直播
     *
     * @param searchDTO
     * @return
     */
    @PostMapping("/api/media-resource/broadcast/page")
    Result<PageDTO<MediaResourceDTO>> searchBroadcastByTitle(@RequestBody BroadcastSearchDTO searchDTO);

    @PostMapping("/api/media-resource/live-broadcast/page")
    Result<PageDTO<MediaResourceDTO>> liveBroadcastPage(@RequestBody LiveBroadcastSearchDTO searchDTO);

    @PostMapping("/api/media-resource/broadcast/page/by/type")
    Result<PageDTO<MediaResourceDTO>> searchBroadcastByTitleAndType(@RequestBody BroadcastSearchDTO searchDTO, @RequestParam Integer contentType);

    @PostMapping("/api/change/live/type")
    Result<Void> changeType(@RequestParam Long id, @RequestParam Integer type);

    /**
     * 竖视频查询
     *
     * @param cursor
     * @return
     */
    @GetMapping("/api/media/fetch/details")
    Result<PageDTO<MediaResourceDTO>> queryByCursor(@RequestParam String cursor);

    /**
     * 通过ids获取媒体资源对象
     *
     * @param mediaIds
     * @return
     */
    @PostMapping("/api/media/fetch/by/ids")
    Result<List<MediaResourceDTO>> fetchMediaByIds(@RequestBody Collection<Long> mediaIds);

    /**
     * 获取媒体赣云关联对象
     *  FIXME: 2023/4/3 过期接口下个版本发布后删除
     *
     * @param mediaIds
     * @return
     */
    @Deprecated
    @PostMapping("/api/media-ganyun-relate/fetch/by/mediaIds")
    Result<List<MediaResourceExternalDTO>> fetchGanyunRelateByMediaIds(@RequestBody List<Long> mediaIds);

    @GetMapping("/api/media/fetch/releaseId")
    Result<List<Long>> fetchMediaReleaseIds(@RequestParam Integer type);

    @PostMapping("/api/media-resources/listRelateByContentId")
    Result<List<MediaResourceDTO>> listRelateByContentId(@RequestBody List<Long> contentIds);

    @PutMapping("/api/media-resources/algorithmLabel")
    Result<Void> updateAlgorithmLabel(@RequestBody List<MediaResourceDTO> mediaResourceDTOList);

    @PutMapping("/api/media-resources/answer")
    Result<Boolean> readAnswer(@RequestBody List<MediaResourceDTO> mediaResourceDTOList);

    @GetMapping("/api/media-resources/answer")
    Result<AnswerReminderPopUpDTO> getAnswerReminderByUserId(@RequestParam Long userId);

    /**
     * 创建今日聊资源
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/media-resource/create/todayTalk")
    Result<Long> createTodayTalk(@RequestBody MediaResourceDTO dto);

    @GetMapping("/api/media-resources/simple/{id}")
    Result<MediaResourceDTO> simpleQueryById(@PathVariable Long id);
}
