package com.jxntv.gvideo.app.be.model.vo.race;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 比赛赛程前端展示对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "比赛赛程VO", description = "比赛赛程VO")
public class RaceScheduleVO {

    @ApiModelProperty(value = "赛程id")
    private Long id;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "比赛标题（轮次）")
    private String title;

    @ApiModelProperty(value = "比赛活动简介")
    private String introduction;

    @ApiModelProperty(value = "主队id")
    private Long homeTeamId;

    @ApiModelProperty(value = "客队id")
    private Long awayTeamId;

    @ApiModelProperty(value = "主队名称")
    private String homeTeamName;

    @ApiModelProperty(value = "主队icon")
    private String homeTeamIcon;

    @ApiModelProperty(value = "客队名称")
    private String awayTeamName;

    @ApiModelProperty(value = "客队icon")
    private String awayTeamIcon;

    @ApiModelProperty(value = "主队得分")
    private Integer homeTeamScore;

    @ApiModelProperty(value = "客队得分")
    private Integer awayTeamScore;

    @ApiModelProperty(value = "主队助威数（初始值）")
    private Integer homeTeamCheer;

    @ApiModelProperty(value = "客队助威数（初始值）")
    private Integer awayTeamCheer;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    @ApiModelProperty(value = "状态：0-未开始、1-进行中、2-已结束")
    private Integer status;

    @ApiModelProperty(value = "是否已预约")
    private Boolean subscribed = Boolean.FALSE;

    @ApiModelProperty(value = "资源信息")
    private ResourceVO resourceVO;

    @Data
    public static class ResourceVO {
        @ApiModelProperty(value = "资源ID")
        private Long id;

        @ApiModelProperty(value = "资源大类 1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏 9 图文 10 语音 11 文章大图/三图 12 文章左文右图 13 外链 14 特殊展示块 15 滚动文章块 17 横向滚动块 23 自建频道直播", required = true, example = "1", notes = "1视频2FM")
        private Integer mediaType;
    }
}