package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AddMediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalCategoryDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.fallback.MediaResourceExternalClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "media-service", contextId = "media-ganyun", fallbackFactory = MediaResourceExternalClientFallback.class)
public interface MediaResourceExternalClient {

    @GetMapping("/api/media-resources/ganyun-relate")
    Result<MediaResourceExternalDTO> getByMediaId(@RequestParam Long mediaId);

    @PostMapping("/api/media-resources/ganyun-relate/batch")
    Result<List<MediaResourceExternalDTO>> batchGetByMediaIds(@RequestBody List<Long> mediaIds);

    @GetMapping("/api/media-resources/ganyun-relate/byContentId")
    Result<MediaResourceExternalDTO> getByContentId(@RequestParam String contentId);

    /**
     * 增加外源资源
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/media-resources/external/add")
    Result<Long> addExternal(@RequestBody AddMediaResourceExternalDTO dto);


    /**
     * 获取分类
     */
    @GetMapping("/api/media-resources/external/category")
    Result<List<MediaResourceExternalCategoryDTO>> getCategoryByMediaId(@RequestParam Long mediaId);

    /**
     * 批量获取分类
     */
    @PostMapping("/api/media-resources/external/category/batch")
    Result<List<MediaResourceExternalCategoryDTO>> batchGetCategoryByMediaIds(@RequestBody List<Long> mediaIds);


}
