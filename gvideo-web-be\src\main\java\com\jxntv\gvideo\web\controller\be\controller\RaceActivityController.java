package com.jxntv.gvideo.web.controller.be.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.RaceActivityClient;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceActivitySearchDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleSearchDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamSearchDTO;
import com.jxntv.gvideo.track.sdk.log.SysLogGroup;
import com.jxntv.gvideo.track.sdk.log.Syslog;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.race.RaceActivityConverter;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.login.thread.UserInfo;
import com.jxntv.gvideo.web.controller.be.model.vo.DictVo;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceActivityVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceLiveBroadcastVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceScheduleVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceTeamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 球赛活动管理控制器 - Web端管理 提供活动、队伍、赛程的管理功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/race/activity")
@Api(value = "球赛活动管理接口", tags = {"球赛活动管理接口"})
public class RaceActivityController {

    @Resource
    private RaceActivityClient raceActivityClient;

    @Resource
    private RaceActivityConverter raceActivityConverter;

    @Resource
    private SysUserClient sysUserService;
    // ==================== 活动管理 ====================

    /**
     * 分页查询活动信息
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param id 活动ID
     * @param title 活动主题
     * @param status 活动状态
     * @param startTime 活动开始时间（查询范围起始）
     * @param endTime 活动结束时间（查询范围截止）
     * @return 活动分页数据
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询活动信息", notes = "分页查询球赛活动信息")
    public Result<Page<RaceActivityVO>> getActivityPage(@ApiParam(name = "current", value = "当前页码", required = false, defaultValue = "1") @RequestParam(required = false, defaultValue = "1") Integer current, @ApiParam(name = "size", value = "每页大小", required = false, defaultValue = "10") @RequestParam(required = false, defaultValue = "10") Integer size, @ApiParam(name = "id", value = "活动ID", required = false) @RequestParam(required = false) Long id, @ApiParam(name = "title", value = "活动主题", required = false) @RequestParam(required = false) String title, @ApiParam(name = "status", value = "活动状态：0-未开始、1-进行中、2-已结束", required = false) @RequestParam(required = false) Integer status, @ApiParam(name = "startTime", value = "活动开始时间（查询范围起始）", required = false) @RequestParam(required = false) String startTime, @ApiParam(name = "endTime", value = "活动结束时间（查询范围截止）", required = false) @RequestParam(required = false) String endTime) {

        // 直接构建查询DTO
        RaceActivitySearchDTO searchDTO = new RaceActivitySearchDTO();
        searchDTO.setCurrent(current);
        searchDTO.setSize(size);
        searchDTO.setId(id);
        searchDTO.setTitle(title);
        searchDTO.setStatus(status);
        searchDTO.setStartTime(startTime);
        searchDTO.setEndTime(endTime);

        // 调用远程服务查询
        Result<PageDTO<RaceActivityDTO>> result = raceActivityClient.page(searchDTO);
        if (!result.callSuccess()) {
            log.error("查询活动列表失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        PageDTO<RaceActivityDTO> pageData = result.getResult();
        if (Objects.isNull(pageData)) {
            return Result.ok(Page.empty(1, 10));
        }

        Page<RaceActivityVO> resultPage = Page.pageOf(pageData, dto -> raceActivityConverter.convertToVo(dto), RaceActivityVO.class);

        return Result.ok(resultPage);
    }

    /**
     * 获取活动列表
     *
     * @return 活动列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取活动列表", notes = "获取活动列表")
    public Result<List<DictVo>> getActivityList() {

        // 直接构建查询DTO
        RaceActivitySearchDTO searchDTO = new RaceActivitySearchDTO();
        searchDTO.setCurrent(1);
        searchDTO.setSize(1000);
        // 调用远程服务查询
        Result<PageDTO<RaceActivityDTO>> result = raceActivityClient.page(searchDTO);
        if (!result.callSuccess() || Objects.isNull(result.getResult())) {
            log.error("查询活动列表失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        if (CollectionUtils.isEmpty(result.getResult().getList())) {
            return Result.ok(Collections.emptyList());
        }

        // 将活动列表转换为字典列表
        List<DictVo> dictList = result.getResult().getList().stream().map(dto -> {
            DictVo vo = new DictVo();
            vo.setId(dto.getId());
            vo.setName(dto.getTitle());
            return vo;
        }).collect(Collectors.toList());

        return Result.ok(dictList);
    }

    /**
     * 新增活动信息
     *
     * @param vo 活动信息
     * @return 处理结果
     */
    @PostMapping
    @ApiOperation(value = "新增活动信息", notes = "新增球赛活动信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-新增球赛活动信息")
    public Result<Boolean> addActivity(@RequestBody @Validated RaceActivityVO vo) {
        // 转换为DTO
        RaceActivityDTO dto = raceActivityConverter.convertToDto(vo);
        // 调用远程服务新增
        Result<Boolean> result = raceActivityClient.addActivity(dto);
        if (!result.callSuccess()) {
            log.error("新增活动失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }
        log.info("新增活动成功，活动名称: {}", vo.getTitle());
        return Result.ok(result.getResult());
    }

    /**
     * 修改活动信息
     *
     * @param vo 活动信息
     * @return 处理结果
     */
    @PutMapping
    @ApiOperation(value = "修改活动信息", notes = "修改球赛活动信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-修改球赛活动信息")
    public Result<Boolean> updateActivity(@RequestBody @Validated RaceActivityVO vo) {

        if (Objects.isNull(vo.getId())) {
            return Result.fail("活动ID不能为空");
        }
        // 转换为DTO
        RaceActivityDTO dto = raceActivityConverter.convertToDto(vo);

        // 调用远程服务修改
        Result<Boolean> result = raceActivityClient.updateActivity(dto);
        if (!result.callSuccess()) {
            log.error("修改活动失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("修改活动成功，活动ID: {}, 活动名称: {}", vo.getId(), vo.getTitle());
        return Result.ok(result.getResult());
    }

    /**
     * 获取活动详情
     *
     * @param id 活动ID
     * @return 活动详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取活动详情", notes = "根据ID获取活动详情")
    public Result<RaceActivityVO> getActivity(@PathVariable Long id) {

        if (Objects.isNull(id)) {
            return Result.fail("活动ID不能为空");
        }

        // 调用远程服务查询
        Result<RaceActivityDTO> result = raceActivityClient.getActivityById(id);
        if (!result.callSuccess()) {
            log.error("查询活动详情失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        RaceActivityVO vo = raceActivityConverter.convertToVo(result.getResult());
        return Result.ok(vo);
    }

    // ==================== 队伍管理 ====================

    /**
     * 分页查询队伍信息
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param id 队伍ID
     * @param activityId 活动ID
     * @param title 队伍名称
     * @return 队伍分页数据
     */
    @GetMapping("/team/page")
    @ApiOperation(value = "分页查询队伍信息", notes = "分页查询球赛队伍信息")
    public Result<Page<RaceTeamVO>> getTeamPage(@ApiParam(name = "current", value = "当前页码", required = false, defaultValue = "1") @RequestParam(required = false, defaultValue = "1") Integer current, @ApiParam(name = "size", value = "每页大小", required = false, defaultValue = "10") @RequestParam(required = false, defaultValue = "10") Integer size, @ApiParam(name = "id", value = "队伍ID", required = false) @RequestParam(required = false) Long id, @ApiParam(name = "activityId", value = "活动ID", required = false) @RequestParam(required = false) Long activityId, @ApiParam(name = "title", value = "队伍名称", required = false) @RequestParam(required = false) String title) {

        // 直接构建查询DTO
        RaceTeamSearchDTO searchDTO = new RaceTeamSearchDTO();
        searchDTO.setCurrent(current);
        searchDTO.setSize(size);
        searchDTO.setId(id);
        searchDTO.setActivityId(activityId);
        searchDTO.setTitle(title);

        // 调用远程服务查询
        Result<PageDTO<RaceTeamDTO>> result = raceActivityClient.teamPage(searchDTO);
        if (!result.callSuccess()) {
            log.error("查询队伍列表失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        PageDTO<RaceTeamDTO> pageData = result.getResult();
        if (Objects.isNull(pageData)) {
            return Result.ok(Page.empty(1, 10));
        }

        Page<RaceTeamVO> resultPage = Page.pageOf(pageData, dto -> raceActivityConverter.convertTeamToVo(dto), RaceTeamVO.class);

        return Result.ok(resultPage);
    }

    /**
     * 新增队伍信息
     *
     * @param vo 队伍信息
     * @return 处理结果
     */
    @PostMapping("/team")
    @ApiOperation(value = "新增队伍信息", notes = "新增球赛队伍信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-新增队伍信息")
    public Result<Boolean> addTeam(@RequestBody @Validated RaceTeamVO vo) {

        // 转换为DTO
        RaceTeamDTO dto = raceActivityConverter.convertTeamToDto(vo);

        // 调用远程服务新增
        Result<Boolean> result = raceActivityClient.addTeam(dto);
        if (!result.callSuccess()) {
            log.error("新增队伍失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("新增队伍成功，队伍名称: {}", vo.getTitle());
        return Result.ok(result.getResult());
    }

    /**
     * 修改队伍信息
     *
     * @param vo 队伍信息
     * @return 处理结果
     */
    @PutMapping("/team")
    @ApiOperation(value = "修改队伍信息", notes = "修改球赛队伍信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-修改队伍信息")
    public Result<Boolean> updateTeam(@RequestBody @Validated RaceTeamVO vo) {

        if (Objects.isNull(vo.getId())) {
            return Result.fail("队伍ID不能为空");
        }

        // 转换为DTO
        RaceTeamDTO dto = raceActivityConverter.convertTeamToDto(vo);

        // 调用远程服务修改
        Result<Boolean> result = raceActivityClient.updateTeam(dto);
        if (!result.callSuccess()) {
            log.error("修改队伍失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("修改队伍成功，队伍ID: {}, 队伍名称: {}", vo.getId(), vo.getTitle());
        return Result.ok(result.getResult());
    }

    /**
     * 获取队伍详情
     *
     * @param id 队伍ID
     * @return 队伍详情
     */
    @GetMapping("/team/{id}")
    @ApiOperation(value = "获取队伍详情", notes = "根据ID获取队伍详情")
    public Result<RaceTeamVO> getTeam(@PathVariable Long id) {

        if (Objects.isNull(id)) {
            return Result.fail("队伍ID不能为空");
        }
        // 调用远程服务查询
        Result<RaceTeamDTO> result = raceActivityClient.getTeamById(id);
        if (!result.callSuccess()) {
            log.error("查询队伍详情失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        RaceTeamVO vo = raceActivityConverter.convertTeamToVo(result.getResult());
        return Result.ok(vo);
    }

    /**
     * 根据活动ID获取队伍列表
     *
     * @param activityId 活动ID
     * @return 队伍列表
     */
    @GetMapping("/team/list")
    @ApiOperation(value = "获取活动队伍列表", notes = "根据活动ID获取队伍列表")
    public Result<List<DictVo>> getTeamList(@ApiParam(name = "activityId", value = "活动ID", required = true) @RequestParam Long activityId) {

        if (Objects.isNull(activityId)) {
            return Result.fail("活动ID不能为空");
        }
        // 直接构建查询DTO
        RaceTeamSearchDTO searchDTO = new RaceTeamSearchDTO();
        searchDTO.setCurrent(1);
        searchDTO.setSize(1000);
        searchDTO.setActivityId(activityId);
        // 调用远程服务查询
        Result<PageDTO<RaceTeamDTO>> result = raceActivityClient.teamPage(searchDTO);
        if (!result.callSuccess() || Objects.isNull(result.getResult())) {
            log.error("查询队伍列表失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        if (CollectionUtils.isEmpty(result.getResult().getList())) {
            return Result.ok(Collections.emptyList());
        }

        // 将活动列表转换为字典列表
        List<DictVo> dictList = result.getResult().getList().stream().map(dto -> {
            DictVo vo = new DictVo();
            vo.setId(dto.getId());
            vo.setName(dto.getTitle());
            return vo;
        }).collect(Collectors.toList());

        return Result.ok(dictList);
    }

    // ==================== 赛程管理 ====================

    /**
     * 分页查询赛程信息
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param id 赛程ID
     * @param activityId 活动ID
     * @param title 比赛标题（轮次）
     * @param homeTeamId 主队ID
     * @param awayTeamId 客队ID
     * @param status 状态：0-未开始、1-进行中、2-已结束
     * @param resourceId 资源ID
     * @param startTime 开始时间（查询范围起始）
     * @param endTime 结束时间（查询范围截止）
     * @return 赛程分页数据
     */
    @GetMapping("/schedule/page")
    @ApiOperation(value = "分页查询赛程信息", notes = "分页查询球赛赛程信息")
    public Result<Page<RaceScheduleVO>> getSchedulePage(@ApiParam(name = "current", value = "当前页码", required = false, defaultValue = "1") @RequestParam(required = false, defaultValue = "1") Integer current, @ApiParam(name = "size", value = "每页大小", required = false, defaultValue = "10") @RequestParam(required = false, defaultValue = "10") Integer size, @ApiParam(name = "id", value = "赛程ID", required = false) @RequestParam(required = false) Long id, @ApiParam(name = "activityId", value = "活动ID", required = false) @RequestParam(required = false) Long activityId, @ApiParam(name = "title", value = "比赛标题（轮次）", required = false) @RequestParam(required = false) String title, @ApiParam(name = "homeTeamId", value = "主队ID", required = false) @RequestParam(required = false) Long homeTeamId, @ApiParam(name = "awayTeamId", value = "客队ID", required = false) @RequestParam(required = false) Long awayTeamId, @ApiParam(name = "status", value = "状态：0-未开始、1-进行中、2-已结束", required = false) @RequestParam(required = false) Integer status, @ApiParam(name = "resourceId", value = "资源ID", required = false) @RequestParam(required = false) Long resourceId, @ApiParam(name = "startTime", value = "开始时间（查询范围起始）", required = false) @RequestParam(required = false) String startTime, @ApiParam(name = "endTime", value = "结束时间（查询范围截止）", required = false) @RequestParam(required = false) String endTime) {

        // 直接构建查询DTO
        RaceScheduleSearchDTO searchDTO = new RaceScheduleSearchDTO();
        searchDTO.setCurrent(current);
        searchDTO.setSize(size);
        searchDTO.setId(id);
        searchDTO.setActivityId(activityId);
        searchDTO.setTitle(title);
        searchDTO.setHomeTeamId(homeTeamId);
        searchDTO.setAwayTeamId(awayTeamId);
        searchDTO.setStatus(status);
        searchDTO.setResourceId(resourceId);
        searchDTO.setStartTime(startTime);
        searchDTO.setEndTime(endTime);
        searchDTO.setSorts(Arrays.asList(SearchDTO.Sort.of("status", true), SearchDTO.Sort.of("startTime", true), SearchDTO.Sort.of("id", false)));

        // 调用远程服务查询
        Result<PageDTO<RaceScheduleDTO>> result = raceActivityClient.schedulePage(searchDTO);
        if (!result.callSuccess()) {
            log.error("查询赛程列表失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        PageDTO<RaceScheduleDTO> pageData = result.getResult();
        if (Objects.isNull(pageData)) {
            return Result.ok(Page.empty(1, 10));
        }
        Page<RaceScheduleVO> resultPage = Page.pageOf(pageData, raceActivityConverter::convertScheduleToVo, RaceScheduleVO.class);
        return Result.ok(resultPage);
    }

    /**
     * 新增赛程信息
     *
     * @param vo 赛程信息
     * @return 处理结果
     */
    @PostMapping("/schedule")
    @ApiOperation(value = "新增赛程信息", notes = "新增球赛赛程信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-新增赛程信息")
    public Result<Boolean> addSchedule(@RequestBody @Validated RaceScheduleVO vo) {

        // 转换为DTO
        RaceScheduleDTO dto = raceActivityConverter.convertScheduleToDto(vo);

        // 调用远程服务新增
        Result<Boolean> result = raceActivityClient.addSchedule(dto);
        if (!result.callSuccess()) {
            log.error("新增赛程失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("新增赛程成功，赛程标题: {}", vo.getTitle());
        return Result.ok(result.getResult());
    }

    /**
     * 修改赛程信息
     *
     * @param vo 赛程信息
     * @return 处理结果
     */
    @PutMapping("/schedule")
    @ApiOperation(value = "修改赛程信息", notes = "修改球赛赛程信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-修改赛程信息")
    public Result<Boolean> updateSchedule(@RequestBody @Validated RaceScheduleVO vo) {

        if (Objects.isNull(vo.getId())) {
            return Result.fail("赛程ID不能为空");
        }

        // 转换为DTO
        RaceScheduleDTO dto = raceActivityConverter.convertScheduleToDto(vo);

        // 调用远程服务修改
        Result<Boolean> result = raceActivityClient.updateSchedule(dto);
        if (!result.callSuccess()) {
            log.error("修改赛程失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("修改赛程成功，赛程ID: {}, 赛程标题: {}", vo.getId(), vo.getTitle());
        return Result.ok(result.getResult());
    }

    /**
     * 获取赛程详情
     *
     * @param id 赛程ID
     * @return 赛程详情
     */
    @GetMapping("/schedule/{id}")
    @ApiOperation(value = "获取赛程详情", notes = "根据ID获取赛程详情")
    public Result<RaceScheduleVO> getSchedule(@PathVariable Long id) {

        if (Objects.isNull(id)) {
            return Result.fail("赛程ID不能为空");
        }

        // 调用远程服务查询
        Result<RaceScheduleDTO> result = raceActivityClient.getScheduleById(id);
        if (!result.callSuccess()) {
            log.error("查询赛程详情失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        RaceScheduleVO vo = raceActivityConverter.convertScheduleToVo(result.getResult());
        return Result.ok(vo);
    }

    /**
     * 删除赛程
     *
     * @param id 赛程ID
     * @return 删除结果
     */
    @DeleteMapping("/schedule/{id}")
    @ApiOperation(value = "删除赛程", notes = "根据ID删除赛程信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-删除赛程信息")
    public Result<Boolean> deleteSchedule(@PathVariable Long id) {

        if (Objects.isNull(id)) {
            return Result.fail("赛程ID不能为空");
        }

        // 调用远程服务删除
        Result<Boolean> result = raceActivityClient.deleteSchedule(id);
        if (!result.callSuccess()) {
            log.error("删除赛程失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("删除赛程成功，赛程ID: {}", id);
        return Result.ok(result.getResult());
    }

    // ==================== 比赛直播管理 ====================

    /**
     * 分页查询比赛直播信息
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param id 比赛直播ID
     * @param activityId 活动ID
     * @param resourceId 资源ID
     * @param delFlag 删除标志 0-未删除 1-已删除
     * @return 比赛直播分页数据
     */
    @GetMapping("/live/broadcast/page")
    @ApiOperation(value = "分页查询比赛直播信息", notes = "分页查询比赛直播信息")
    public Result<Page<RaceLiveBroadcastVO>> getLiveBroadcastPage(@ApiParam(name = "current", value = "当前页码", required = false, defaultValue = "1") @RequestParam(required = false, defaultValue = "1") Integer current, @ApiParam(name = "size", value = "每页大小", required = false, defaultValue = "10") @RequestParam(required = false, defaultValue = "10") Integer size, @ApiParam(name = "id", value = "比赛直播ID", required = false) @RequestParam(required = false) Long id, @ApiParam(name = "activityId", value = "活动ID", required = false) @RequestParam(required = false) Long activityId, @ApiParam(name = "resourceId", value = "资源ID", required = false) @RequestParam(required = false) Long resourceId, @ApiParam(name = "delFlag", value = "删除标志 0-未删除 1-已删除", required = false) @RequestParam(required = false) Integer delFlag) {

        // 直接构建查询DTO
        RaceLiveBroadcastSearchDTO searchDTO = new RaceLiveBroadcastSearchDTO();
        searchDTO.setCurrent(current);
        searchDTO.setSize(size);
        searchDTO.setId(id);
        searchDTO.setActivityId(activityId);
        searchDTO.setResourceId(resourceId);
        searchDTO.setDelFlag(delFlag);

        // 调用远程服务查询
        Result<PageDTO<RaceLiveBroadcastDTO>> result = raceActivityClient.liveBroadcastPage(searchDTO);
        if (!result.callSuccess()) {
            log.error("查询比赛直播列表失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        PageDTO<RaceLiveBroadcastDTO> pageData = result.getResult();
        if (Objects.isNull(pageData)) {
            return Result.ok(Page.empty(1, 10));
        }

        Page<RaceLiveBroadcastVO> resultPage = Page.pageOf(pageData, dto -> raceActivityConverter.convertLiveBroadcastToVo(dto), RaceLiveBroadcastVO.class);

        return Result.ok(resultPage);
    }

    /**
     * 新增比赛直播信息
     *
     * @param vo 比赛直播信息
     * @return 处理结果
     */
    @PostMapping("/live/broadcast")
    @ApiOperation(value = "新增比赛直播信息", notes = "新增比赛直播信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-新增比赛直播信息")
    public Result<Boolean> addLiveBroadcast(@RequestBody @Validated RaceLiveBroadcastVO vo) {
        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.isNull(userInfo)) {
            return Result.fail("用户未登录");
        }
        Result<SysUserDTO> sysUserDTOResult = sysUserService.getUserInfo(userInfo.getUsername());
        if (sysUserDTOResult.getCode() != CodeMessage.OK.getCode() || Objects.isNull(sysUserDTOResult.getResult())) {
            return Result.fail("获取用户信息失败");
        }
        // 转换为DTO
        RaceLiveBroadcastDTO dto = raceActivityConverter.convertLiveBroadcastToDto(vo);
        dto.setUpdateUserId(sysUserDTOResult.getResult().getId());
        dto.setUpdateUserName(sysUserDTOResult.getResult().getUsername());
        // 调用远程服务新增
        Result<Boolean> result = raceActivityClient.addLiveBroadcast(dto);
        if (!result.callSuccess()) {
            log.error("新增比赛直播失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }
        log.info("新增比赛直播成功，比赛直播ID: {}", vo.getId());
        return Result.ok(result.getResult());
    }

    /**
     * 修改比赛直播信息
     *
     * @param vo 比赛直播信息
     * @return 处理结果
     */
    @PutMapping("/live/broadcast")
    @ApiOperation(value = "修改比赛直播信息", notes = "修改比赛直播信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-修改比赛直播信息")
    public Result<Boolean> updateLiveBroadcast(@RequestBody @Validated RaceLiveBroadcastVO vo) {

        if (Objects.isNull(vo.getId())) {
            return Result.fail("比赛直播ID不能为空");
        }


        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.isNull(userInfo)) {
            return Result.fail("用户未登录");
        }
        Result<SysUserDTO> sysUserDTOResult = sysUserService.getUserInfo(userInfo.getUsername());
        if (sysUserDTOResult.getCode() != CodeMessage.OK.getCode() || Objects.isNull(sysUserDTOResult.getResult())) {
            return Result.fail("获取用户信息失败");
        }
        // 转换为DTO
        RaceLiveBroadcastDTO dto = raceActivityConverter.convertLiveBroadcastToDto(vo);
        dto.setUpdateUserId(sysUserDTOResult.getResult().getId());
        dto.setUpdateUserName(sysUserDTOResult.getResult().getUsername());

        // 调用远程服务修改
        Result<Boolean> result = raceActivityClient.updateLiveBroadcast(dto);
        if (!result.callSuccess()) {
            log.error("修改比赛直播失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("修改比赛直播成功，比赛直播ID: {}", vo.getId());
        return Result.ok(result.getResult());
    }

    /**
     * 获取比赛直播详情
     *
     * @param id 比赛直播ID
     * @return 比赛直播详情
     */
    @GetMapping("/live/broadcast/{id}")
    @ApiOperation(value = "获取比赛直播详情", notes = "根据ID获取比赛直播详情")
    public Result<RaceLiveBroadcastVO> getLiveBroadcast(@PathVariable Long id) {

        if (Objects.isNull(id)) {
            return Result.fail("比赛直播ID不能为空");
        }

        // 调用远程服务查询
        Result<RaceLiveBroadcastDTO> result = raceActivityClient.getLiveBroadcastById(id);
        if (!result.callSuccess()) {
            log.error("查询比赛直播详情失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        RaceLiveBroadcastVO vo = raceActivityConverter.convertLiveBroadcastToVo(result.getResult());
        return Result.ok(vo);
    }

    /**
     * 删除比赛直播
     *
     * @param id 比赛直播ID
     * @return 删除结果
     */
    @DeleteMapping("/live/broadcast/{id}")
    @ApiOperation(value = "删除比赛直播", notes = "根据ID删除比赛直播信息")
    @Syslog(type = SysLogGroup.CONTENT_RESOURCE, subject = "球赛活动-删除比赛直播信息")
    public Result<Boolean> deleteLiveBroadcast(@PathVariable Long id) {

        if (Objects.isNull(id)) {
            return Result.fail("比赛直播ID不能为空");
        }

        // 调用远程服务删除
        Result<Boolean> result = raceActivityClient.deleteLiveBroadcast(id);
        if (!result.callSuccess()) {
            log.error("删除比赛直播失败: {}", result.getMessage());
            return Result.fail(result.getCode(), result.getMessage());
        }

        log.info("删除比赛直播成功，比赛直播ID: {}", id);
        return Result.ok(result.getResult());
    }

}
