package com.jxntv.gvideo.media.client.dto.race;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 比赛直播搜索条件数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RaceLiveBroadcastSearchDTO extends SearchDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 队伍id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 标题
     */
    private String title;

    /**
     * 删除标志 0-未删除 1-已删除
     */
    private Integer delFlag;

}
