package com.jxntv.gvideo.media.api.domain.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxntv.gvideo.media.api.domain.entity.handler.RedActivityJudgeTrackConfigHandler;
import com.jxntv.gvideo.media.client.dto.redactivity.RedActivityJudgeTrackConfig;
import lombok.Data;

@Data
@TableName(value = "red_activity_judge", autoResultMap = true)
public class RedActivityJudge {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动ID
     */
    @TableField("activity_id")
    private Long activityId;

    @TableField("judge_phone")
    private String judgePhone;

    @TableField("judge_type")
    private String judgeType;

    @TableField(typeHandler = RedActivityJudgeTrackConfigHandler.class)
    private RedActivityJudgeTrackConfig judgeTrack;

    @TableField("create_time")
    private Date createTime;
}
