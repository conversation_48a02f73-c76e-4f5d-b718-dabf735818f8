package com.jxntv.gvideo.media.api.event.listener;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.OptionalDouble;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxntv.gvideo.media.api.domain.entity.RedActivityActive;
import com.jxntv.gvideo.media.api.domain.entity.RedActivityJudgeScore;
import com.jxntv.gvideo.media.api.event.RedActivityProductScoreEvent;
import com.jxntv.gvideo.media.api.repository.RedActivityActiveMapper;
import com.jxntv.gvideo.media.api.repository.RedActivityJudgeScoreMapper;
import com.jxntv.gvideo.media.api.service.RedActivityService;
import lombok.extern.slf4j.Slf4j;

/**
 * 处理提问发送短信
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class RedActivityProductScoreListener {


    @Resource
    private RedActivityService redActivityService;

    @Resource
    private RedActivityJudgeScoreMapper scoreMapper;


    @Resource
    private RedActivityActiveMapper activeMapper;

    @Async
    @EventListener
    public void onEvent(RedActivityProductScoreEvent event) {
        log.info("红色回声-评委打分事件:{}", event);

        RedActivityActive active = activeMapper.selectById(event.getProductId());
        if (Objects.isNull(active)) {
            return;
        }

        List<RedActivityJudgeScore> scoreList = scoreMapper.selectList(new LambdaQueryWrapper<RedActivityJudgeScore>().eq(RedActivityJudgeScore::getProductId, event.getProductId()).orderByAsc(RedActivityJudgeScore::getId).last("limit 5"));
        if (!CollectionUtils.isEmpty(scoreList) && scoreList.size() > 2) {
            // 使用lambda表达式移除list里的最高分和最低分，然后计算剩余分数的平均分
            List<Integer> scores = scoreList.stream().map(RedActivityJudgeScore::getScore).collect(Collectors.toList());

            // 找到最高分和最低分
            Integer maxScore = scores.stream().mapToInt(Integer::intValue).max().orElse(0);
            Integer minScore = scores.stream().mapToInt(Integer::intValue).min().orElse(0);

            // 移除一个最高分和一个最低分
            scores.remove(maxScore);
            scores.remove(minScore);

            // 计算剩余分数的平均分，保留一位小数
            OptionalDouble averageScore = scores.stream().mapToInt(Integer::intValue).average();
            if (averageScore.isPresent()) {
                // 使用BigDecimal保留一位小数
                BigDecimal finalScore = new BigDecimal(averageScore.getAsDouble()).setScale(1, RoundingMode.HALF_UP);
                log.info("产品ID {} 的最终平均分为: {}", event.getProductId(), finalScore);

                active.setScore(finalScore);
                active.setUpdateTime(new Date());
                activeMapper.updateById(active);

            }
        }

    }
}
