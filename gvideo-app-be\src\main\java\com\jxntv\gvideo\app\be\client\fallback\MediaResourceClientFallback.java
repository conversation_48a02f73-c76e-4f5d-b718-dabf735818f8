package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.dto.search.BroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.LiveBroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceSearchDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MediaResourceClientFallback implements FallbackFactory<MediaResourceClient> {
    @Override
    public MediaResourceClient create(Throwable throwable) {
        return new MediaResourceClient() {
            @Override
            public Result<List<MediaResourceDTO>> feed(String cursor, Long tabId) {
                log.error("MediaResourceClient.feed() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Map<String, List<Long>>> feedReleaseVideo() {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<Long>> feedReleaseVideo(String device) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<Long>> feedVideo(Integer maxRank) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<Long>> feedVideo(Integer pageNum, String device) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> page(MediaResourceSearchDTO searchDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> listResourcePage(MediaResourceSearchDTO searchDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> listAuthorContentPage(MediaResourceSearchDTO searchDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaResourceDTO> get(Long id) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceImageDTO>> getImages(Long id) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaResourceLocationDTO> getLocation(Long id) {
                log.error("MediaResourceClient.getLocation() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceLabelDTO>> getLabels(Long id) {
                log.error("MediaResourceClient.getLabels() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceLabelDTO>> batchGetLabelsByIds(List<Long> ids) {
                log.error("MediaResourceClient.batchGetLabelsByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceDTO>> bulk(List<Long> ids) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceDTO>> baseInfoBulk(List<Long> ids) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> create(MediaResourceDTO mediaResourceDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> createUgc(MediaUgcFileResourceDTO mediaUgcFileResourceDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> updateUgc(MediaUgcFileResourceDTO mediaFileResourceDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> ugcWorksCount(long releaseId, Boolean isIncludePrivacy) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result update(Long id, MediaResourceDTO mediaResourceDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result updateSimple(Long id, MediaResourceDTO mediaResourceDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result updateAuditStatus(Long id, MediaResourceDTO mediaResourceDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaResourcePendantDTO> getPendant(Long id) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourcePendantDTO>> listPendantByIds(List<Long> mediaIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result savePendant(Long id, MediaResourcePendantDTO dto) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result removeMediaPendant(List<Long> mediaIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceDTO>> fuzzy(List<Integer> contentTypeList, String internalName) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceDTO>> fuzzyPptv(List<Integer> contentTypeList, String showName) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<SysConfigDTO>> configs() {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> countByCertificationId(Long certificationId) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> getOwnMedias(long authorId, int current, int size) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> countByColumnId(Long columnId) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result batchDiscard(List<Long> mediaIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> discardUgc(Long ugcId) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> statusModify(Long id, Integer status) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteById(Long id) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaCloudResourceDTO>> getMediaCloudResourceList(String mediaCloudIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> fuzzyPage(String key, List<Integer> contentTypes, String excludeType, Integer current, Integer size, Long tenantId, Integer releaseType) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> countByJid(Long jid) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> countEnable(List<Long> mediaLst) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<Long>> getByCondition(List<Long> jidLst, Date start, Date end) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> editNews(MediaResourceDTO dto) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> queryNews(NewsQueryParam dto) {
                log.error("MediaResourceClient.queryNews() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }


            @Override
            public Result<List<MediaResourceDTO>> findByIdsAndContentType(FindByIdsAndContentTypeDTO dto) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> updateLastPassDate(Long mediaId) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> pagePosts(PostSearchDTO searchDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> pageJuniorPosts(PostSearchDTO searchDTO) {
                log.error("MediaResourceClient.pageJuniorPosts() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> followList(Long jid, Integer pageNum) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> searchBroadcastByTitle(BroadcastSearchDTO searchDTO) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> liveBroadcastPage(LiveBroadcastSearchDTO searchDTO) {
                log.error("MediaResourceClient.liveBroadcastPage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> searchBroadcastByTitleAndType(BroadcastSearchDTO searchDTO, Integer contentType) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> changeType(Long id, Integer type) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> queryByCursor(String cursor) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceDTO>> fetchMediaByIds(Collection<Long> mediaIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceExternalDTO>> fetchGanyunRelateByMediaIds(List<Long> mediaIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<Long>> fetchMediaReleaseIds(Integer type) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<MediaResourceDTO>> listRelateByContentId(List<Long> contentIds) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> updateAlgorithmLabel(List<MediaResourceDTO> mediaResourceDTOList) {
                log.error("MediaResourceClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
            @Override
            public Result<Boolean> readAnswer(List<MediaResourceDTO> mediaResourceDTOList) {
                log.error("MediaResourceClient.updateMediaPv() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<AnswerReminderPopUpDTO> getAnswerReminderByUserId(Long userId) {
                log.error("MediaResourceClient.updateMediaPv() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> createTodayTalk(MediaResourceDTO dto) {
                log.error("MediaResourceClient.createTodayTalk() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaResourceDTO> simpleQueryById(Long id) {
                log.error("MediaResourceClient.simpleQueryById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
