package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 比赛直播实体类 对应数据库表race_live_broadcast
 *
 * <AUTHOR>
 */
@Data
@TableName("race_live_broadcast")
public class RaceLiveBroadcast {

    /**
     * 队伍id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 标题
     */
    private String title;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 删除标志 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 创建用户账号
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 更新用户id
     */
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}