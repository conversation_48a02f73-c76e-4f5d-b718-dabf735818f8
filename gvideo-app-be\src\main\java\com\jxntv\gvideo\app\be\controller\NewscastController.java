package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastEntireVO;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastSplitPage;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastSplitVO;
import com.jxntv.gvideo.app.be.service.NewscastService;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.newscast.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/news/")
@Api(value = "新闻联播接口", tags = {"NewsController"})
public class NewscastController {

    @Resource
    private NewscastService newscastService;


    @GetMapping("entire")
    @ApiOperation(value = "新闻联播-整期单个")
    public Result<NewscastEntireVO> getEntire(@ApiParam("发布日期") @RequestParam String publishDate) {
        NewscastEntireVO result = newscastService.getEntire(publishDate);
        return Result.ok(result);
    }


    @GetMapping("entire/list")
    @ApiOperation(value = "新闻联播-整期列表")
    public Result<Page<NewscastEntireVO>> entireList(@ApiParam("分页页号") @RequestParam Integer pageNum,
                                                     @ApiParam("分页大小") @RequestParam Integer pageSize) {
        Page<NewscastEntireVO> result = newscastService.entireList(pageNum, pageSize);
        return Result.ok(result);
    }


    @GetMapping("split/list")
    @ApiOperation(value = "新闻联播-拆条列表")
    public Result<NewscastSplitPage<NewscastSplitVO>> splitList(@ApiParam("发布日期") @RequestParam String publishDate,
                                                                @ApiParam("分页页号") @RequestParam Integer pageNum,
                                                                @ApiParam("分页大小") @RequestParam Integer pageSize) {
        NewscastSplitPage<NewscastSplitVO> result = newscastService.splitList(publishDate, pageNum, pageSize);
        return Result.ok(result);
    }


    @Deprecated
    @GetMapping("/h5/category/list")
    @ApiOperation(value = "新闻联播-整期列表")
    public JxntvCategoryResponse h5CategoryList(JxntvCategroyRequest request) {
        return newscastService.h5CategoryListV2(request);
    }

    @Deprecated
    @GetMapping("/h5/content/list")
    @ApiOperation(value = "新闻联播-拆条列表")
    public JxntvContentResponse h5ContentList(JxntvContentRequest request) {
        return newscastService.h5ContentListV2(request);
    }

    @PostMapping("/h5/getBatchMediaid")
    @ApiOperation(value = "新闻联播-获取今视频ID")
    public JxntvMediaResponse getBatchMediaid(@RequestBody JxntvMediaRequest request) {
        return newscastService.getMediaIds(request.getId());
    }



    @GetMapping("/h5/category/list/v2")
    @ApiOperation(value = "新闻联播-整期列表")
    public JxntvCategoryResponse h5CategoryListV2(JxntvCategroyRequest request) {
        return newscastService.h5CategoryListV2(request);
    }

    @GetMapping("/h5/content/list/v2")
    @ApiOperation(value = "新闻联播-拆条列表")
    public JxntvContentResponse h5ContentListV2(JxntvContentRequest request) {
        return newscastService.h5ContentListV2(request);
    }


}
