package com.jxntv.gvideo.media.api.controller;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.media.api.converter.MediaResourceExternalConverter;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceExternal;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceExternalCategory;
import com.jxntv.gvideo.media.api.service.MediaResourceExternalCategoryService;
import com.jxntv.gvideo.media.api.service.MediaResourceExternalService;
import com.jxntv.gvideo.media.api.service.MediaResourceService;
import com.jxntv.gvideo.media.api.utils.DateUtils;
import com.jxntv.gvideo.media.client.MediaResourceExternalClient;
import com.jxntv.gvideo.media.client.dto.AddMediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalCategoryDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.enums.ExternalPlatform;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class MediaResourceExternalController implements MediaResourceExternalClient {

    @Resource
    private MediaResourceService mediaResourceService;

    @Resource
    private MediaResourceExternalService mediaResourceExternalService;

    @Resource
    private MediaResourceExternalCategoryService mediaResourceExternalCategoryService;

    @Resource
    private MediaResourceExternalConverter mediaResourceExternalConverter;

    @Override
    public Result<MediaResourceExternalDTO> getByMediaId(Long mediaId) {
        MediaResourceExternal mediaResourceExternal = mediaResourceExternalService.getByMediaId(mediaId);
        AssertUtil.notNull(mediaResourceExternal, CodeMessage.NOT_FOUND);
        MediaResourceExternalDTO dto = mediaResourceExternalConverter.convert(mediaResourceExternal);
        return Result.ok(dto);
    }

    @Override
    public Result<MediaResourceExternalDTO> getByContentId(String contentId) {
        MediaResourceExternal mediaResourceExternal = mediaResourceExternalService.geExternal(ExternalPlatform.GanYun.name(), contentId);
        AssertUtil.notNull(mediaResourceExternal, CodeMessage.NOT_FOUND);
        MediaResourceExternalDTO dto = mediaResourceExternalConverter.convert(mediaResourceExternal);
        return Result.ok(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Long> addExternal(AddMediaResourceExternalDTO dto) {
        MediaResourceExternal external = mediaResourceExternalService.geExternal(dto.getPlatform(), dto.getContentId());
        if (Objects.isNull(external)) {
            //  新建资源主体
            MediaResource mediaResource = new MediaResource();
            mediaResource.setStatus(MediaResourceStatus.ENABLE.getCode());
            mediaResource.setShowName(dto.getTitle());
            mediaResource.setInternalName(dto.getTitle());
            mediaResource.setContent(dto.getContent());
            mediaResource.setContentType(dto.getContentType());
            mediaResource.setReleaseId(dto.getAuthorId());
            mediaResource.setReleaseType(0);
            mediaResource.setCreateDate(DateUtils.localDateTimeToDate(dto.getCreateTime()));
            mediaResource.setCreateUserId(1L);
            mediaResource.setTenantId(dto.getTenantId());
            mediaResource.setCanComment(dto.getCanComment());
            mediaResource.setCanSearch(dto.getCanSearch());

            mediaResourceService.save(mediaResource);


            //  新建关联关系
            external = new MediaResourceExternal();
            external.setMediaId(mediaResource.getId());
            external.setContentId(dto.getContentId());
            external.setPlatform(dto.getPlatform());
            external.setCreateDate(dto.getCreateTime());
            external.setCreateUserId(1L);

            mediaResourceExternalService.save(external);
        } else {
            //  更新资源主体
            MediaResource mediaResource = new MediaResource();
            mediaResource.setId(external.getMediaId());
            mediaResource.setStatus(MediaResourceStatus.ENABLE.getCode());
            mediaResource.setShowName(dto.getTitle());
            mediaResource.setInternalName(dto.getTitle());
            mediaResource.setContent(dto.getContent());
            mediaResource.setContentType(dto.getContentType());
            mediaResource.setReleaseId(dto.getAuthorId());
            mediaResource.setReleaseType(0);
            mediaResource.setTenantId(dto.getTenantId());
            mediaResource.setCanComment(dto.getCanComment());
            mediaResource.setCanSearch(dto.getCanSearch());
            mediaResource.setUpdateDate(new Date());
            mediaResource.setUpdateUserId(1L);

            mediaResourceService.updateById(mediaResource);



            // 更新外部资源关联信息
            external.setContentId(dto.getContentId());
            external.setPlatform(dto.getPlatform());
            external.setUpdateDate(LocalDateTime.now());
            external.setUpdateUserId(1L);
            mediaResourceExternalService.updateById(external);
        }


        return Result.ok(external.getMediaId());
    }

    @Override
    public Result<List<MediaResourceExternalCategoryDTO>> getCategoryByMediaId(Long mediaId) {
        List<MediaResourceExternalCategory> categoryList = mediaResourceExternalCategoryService.listByMediaId(mediaId);
        List<MediaResourceExternalCategoryDTO> dtoList = categoryList.stream().map(mediaResourceExternalConverter::convert).collect(Collectors.toList());
        return Result.ok(dtoList);
    }

    @Override
    public Result<List<MediaResourceExternalDTO>> batchGetByMediaIds(List<Long> mediaIds) {
        List<MediaResourceExternal> externalList = mediaResourceExternalService.listByMediaIds(mediaIds);
        List<MediaResourceExternalDTO> dtoList = externalList.stream().map(mediaResourceExternalConverter::convert).collect(Collectors.toList());
        return Result.ok(dtoList);
    }

    @Override
    public Result<List<MediaResourceExternalCategoryDTO>> batchGetCategoryByMediaIds(List<Long> mediaIds) {
        List<MediaResourceExternalCategory> categoryList = mediaResourceExternalCategoryService.listByMediaIds(mediaIds);
        List<MediaResourceExternalCategoryDTO> dtoList = categoryList.stream().map(mediaResourceExternalConverter::convert).collect(Collectors.toList());
        return Result.ok(dtoList);
    }
}
