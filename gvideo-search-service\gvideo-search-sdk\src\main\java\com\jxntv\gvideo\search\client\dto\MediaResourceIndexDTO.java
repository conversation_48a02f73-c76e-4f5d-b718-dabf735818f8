package com.jxntv.gvideo.search.client.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class MediaResourceIndexDTO implements Serializable {

    private String id;

    private String title;

    private String description;

    private String contentId; // 内容ID字段

    private String videoUrl; // 视频url

    private String thumb; // 视频封面图url

    private Integer status; // 状态

    private Long createTime;

    private List<LabelDTO> labels; // 标签嵌套结构

    private List<CategoryDTO> categories; // 分类嵌套结构

    @Data
    public static class LabelDTO {
        private Integer type;// 标签类型

        private Long labelId; // 标签ID

        private String labelName; // 标签名称


    }

    @Data
    public static class CategoryDTO {

        private Long categoryId; // 分类ID


        private String categoryName; // 分类名称


    }
}
