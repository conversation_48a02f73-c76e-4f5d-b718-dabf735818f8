package com.jxntv.gvideo.media.api.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.dto.MediaIdDTO;
import com.jxntv.gvideo.media.api.converter.MediaFileConverter;
import com.jxntv.gvideo.media.api.converter.MediaResourceConverter;
import com.jxntv.gvideo.media.api.converter.MediaResourceLocationConverter;
import com.jxntv.gvideo.media.api.converter.MediaResourcePendantConverter;
import com.jxntv.gvideo.media.api.domain.entity.*;
import com.jxntv.gvideo.media.api.service.*;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.dto.search.BroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.LiveBroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceSearchDTO;
import com.jxntv.gvideo.media.client.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 内容资源管理
 * <p>
 * <p>
 * Created on 2020-02-20
 */
@RestController
@Slf4j
public class MediaResourceController implements MediaResourceClient {

    @Autowired
    private MediaResourceService mediaResourceService;
    @Autowired
    private LabelService labelService;
    @Autowired
    private MediaResourceLabelService mediaResourceLabelService;
    @Autowired
    private MediaResourceImageService mediaResourceImageService;
    @Autowired
    private SysConfigService configService;
    @Autowired
    private MediaResourcePendantService pendantService;
    @Resource
    private MediaResourceConverter mediaResourceConverter;
    @Autowired
    private AdmVideoRecommendationDService admVideoRecommendationDService;
    @Autowired
    private AdmReleaseVideoRecommendationDService admReleaseVideoRecommendationDService;
    @Autowired
    private GroupTopicClient groupTopicClient;
    @Autowired
    private MediaResourceExternalService mediaResourceExternalContentService;
    @Resource
    private MediaFileService mediaFileService;
    @Resource
    private MediaResourceLocationService mediaResourceLocationService;
    @Resource
    private MediaResourceLocationConverter mediaResourceLocationConverter;
    @Resource
    private WenjianService wenjianService;

    /**
     * 初始化标签
     */
    @GetMapping("/api/media-resources/label/init")
    public Result<Void> initLabels() {
        Long startId = 0L;

        while (true) {

            LambdaQueryWrapper<MediaResource> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.and(wrapper -> wrapper.ne(MediaResource::getInternalLabel, "")
                    .or().ne(MediaResource::getContentTypeLabel, "")
                    .or().ne(MediaResource::getOtherLabel, ""));
            lambdaQuery.orderByAsc(MediaResource::getId);
            lambdaQuery.gt(MediaResource::getId, startId);
            lambdaQuery.last("limit 1000");

            List<MediaResource> list = mediaResourceService.list(lambdaQuery);

            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            // 遍历处理
            list.forEach(e -> {
                try {
                    mediaResourceLabelService.saveLabels(e);
                } catch (Exception exception) {
                    log.error("标签同步错误", exception);
                }
            });

            // startId赋值
            startId = list.get(list.size() - 1).getId();
        }

        return Result.ok();
    }

    @Override
    public Result<List<MediaResourceDTO>> feed(String cursor, Long tabId) {
        LambdaQueryWrapper<MediaResource> query = Wrappers.lambdaQuery();
        if (tabId != null) {
            query.eq(MediaResource::getPlayStyle, tabId);
        }
        List<MediaResource> list = mediaResourceService.list(query);
        List<MediaResource> data = new ArrayList<>(5);
        int total = list.size();
        for (int i = 0; i < 5; i++) {
            int index = RandomUtils.nextInt(total - 1);
            data.add(list.get(index));
        }
        return Result.ok(data.stream().map(MediaResourceConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<Map<String, List<Long>>> feedReleaseVideo() {
        return admReleaseVideoRecommendationDService.queryAllReleaseContent();
    }

    @Override
    public Result<List<Long>> feedReleaseVideo(String device) {
        return admReleaseVideoRecommendationDService.queryReleaseContent(device);
    }

    @Override
    public Result<List<Long>> feedVideo(Integer maxRank) {
        return admVideoRecommendationDService.queryContentMaxRank(maxRank);
    }

    @Override
    public Result<PageDTO<Long>> feedVideo(Integer pageNum, String device) {
        return Result.ok(admVideoRecommendationDService.queryContentSearch(device, pageNum));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> page(MediaResourceSearchDTO searchDTO) {
        LambdaQueryWrapper<MediaResource> query = new LambdaQueryWrapper<>();
        if (Objects.nonNull(searchDTO.getId())) {
            query.eq(MediaResource::getId, searchDTO.getId());
        }
        if (!CollectionUtils.isEmpty(searchDTO.getLabelIds())) {
            query.like(MediaResource::getContentTypeLabel, searchDTO.getLabelIds().get(0));
        }
        if (Objects.nonNull(searchDTO.getReleaseType())) {
            query.eq(MediaResource::getReleaseType, searchDTO.getReleaseType());
        }

        if (Objects.nonNull(searchDTO.getPlatform())) {
            query.eq(MediaResource::getPlatform, searchDTO.getPlatform());
        }

        if (!CollectionUtils.isEmpty(searchDTO.getReleaseIds())) {
            if (searchDTO.getReleaseIds().size() == 1) {
                query.eq(MediaResource::getReleaseId, searchDTO.getReleaseIds().get(0));
            } else {
                query.in(MediaResource::getReleaseId, searchDTO.getReleaseIds());
            }
        }
        if (Objects.nonNull(searchDTO.getDataType())) {
            query.eq(MediaResource::getDataType, searchDTO.getDataType());
        }
        if (Objects.nonNull(searchDTO.getFilterDataType())) {
            query.ne(MediaResource::getDataType, searchDTO.getFilterDataType());
        }
        if (!CollectionUtils.isEmpty(searchDTO.getColumnIds())) {
            query.in(MediaResource::getColumnId, searchDTO.getColumnIds());
        }
        if (!CollectionUtils.isEmpty(searchDTO.getTenantIds())) {
            query.in(MediaResource::getTenantId, searchDTO.getTenantIds());
        }
        if (!CollectionUtils.isEmpty(searchDTO.getUpdateUserIds())) {
            query.in(MediaResource::getUpdateUserId, searchDTO.getUpdateUserIds());
        }
        List<Integer> contentTypes = searchDTO.getContentTypes();
        if (!CollectionUtils.isEmpty(contentTypes)) {
            query.in(MediaResource::getContentType, contentTypes);
        }
        // 仅个人作品列表传入该参数
        List<Integer> excludeContentTypes = searchDTO.getExcludeContentTypes();
        if (CollectionUtils.isEmpty(contentTypes) && !CollectionUtils.isEmpty(excludeContentTypes)) {
            query.notIn(MediaResource::getContentType, excludeContentTypes);
        }
        if (!CollectionUtils.isEmpty(searchDTO.getPlayStyles())) {
            query.in(MediaResource::getPlayStyle, searchDTO.getPlayStyles());
        }
        if (searchDTO.getFileId() != null) {
            query.eq(MediaResource::getFileId, searchDTO.getFileId());
        }
        if (!CollectionUtils.isEmpty(searchDTO.getStatus())) {
            query.in(MediaResource::getStatus, searchDTO.getStatus());
        }
        if (Objects.nonNull(searchDTO.getIsSearch())) {
            query.eq(MediaResource::getCanSearch, searchDTO.getIsSearch());
        }
        if (searchDTO.getResourceId() != null) {
            query.eq(MediaResource::getId, searchDTO.getResourceId());
        }
        if (Objects.nonNull(searchDTO.getIsAnonymous())) {
            query.eq(MediaResource::getIsAnonymous, searchDTO.getIsAnonymous());
        }

        if (!CollectionUtils.isEmpty(searchDTO.getMainDataIds())) {
            query.in(MediaResource::getPptvMainDataId, searchDTO.getMainDataIds());
        }

        if (StringUtils.hasText(searchDTO.getShowName())) {
            query.like(MediaResource::getShowName, searchDTO.getShowName());
        }
        if (StringUtils.hasText(searchDTO.getInternalName())) {
            query.like(MediaResource::getInternalName, searchDTO.getInternalName());
        }
        if (Objects.nonNull(searchDTO.getStartId())) {
            query.gt(MediaResource::getId, searchDTO.getStartId());
        }
        if (Objects.nonNull(searchDTO.getEndId())) {
            query.lt(MediaResource::getId, searchDTO.getEndId());
        }
        MediaResourceDateType mediaResourceDateType = MediaResourceDateType.parse(searchDTO.getDateType());
        if (mediaResourceDateType != null) {
            switch (mediaResourceDateType) {
                // 时间类型检索
                case CREATE_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getCreateDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getCreateDate, searchDTO.getEnd());
                    }
                    break;
                case UPDATE_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getUpdateDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getUpdateDate, searchDTO.getEnd());
                    }
                    break;
                case DISCARD_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getDiscardDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getDiscardDate, searchDTO.getEnd());
                    }
                    break;
                case SUBMIT_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getSubmitDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getSubmitDate, searchDTO.getEnd());
                    }
                    break;
                case PASS_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getPassDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getPassDate, searchDTO.getEnd());
                    }
                    break;
                case REPAIR_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getRepairDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getRepairDate, searchDTO.getEnd());
                    }
                    break;
                case ENABLE_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getEnableDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getEnableDate, searchDTO.getEnd());
                    }
                    break;
                case DISABLE_DATE:
                    if (searchDTO.getStart() != null) {
                        query.gt(MediaResource::getDisableDate, searchDTO.getStart());
                    }
                    if (searchDTO.getEnd() != null) {
                        query.lt(MediaResource::getDisableDate, searchDTO.getEnd());
                    }
                    break;
            }
        }
        if (searchDTO.getOrderBy().equals(OrderBy.ID.name())) {
            if (searchDTO.isAsc()) {
                query.orderByAsc(MediaResource::getId);
            } else {
                query.orderByDesc(MediaResource::getId);
            }
        }

        if (searchDTO.getOrderBy().equals(OrderBy.CREATE_DATE.name())) {
            if (searchDTO.isAsc()) {
                query.orderByAsc(MediaResource::getCreateDate);
            } else {
                query.orderByDesc(MediaResource::getCreateDate);
            }
        }

        IPage<MediaResource> page = mediaResourceService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()),
                query);
        return Result.ok(PageUtils.pageOf(page, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> listResourcePage(MediaResourceSearchDTO searchDTO) {
        IPage<Long> page = mediaResourceService
                .listResourcePage(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), searchDTO);
        // 去除重复元素
        page.setRecords(page.getRecords().stream().distinct().collect(Collectors.toList()));
        // id转换元素
        IPage<MediaResource> mediaResourceIPage = page.convert(id -> mediaResourceService.getById(id));

        return Result.ok(PageUtils.pageOf(mediaResourceIPage, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> listAuthorContentPage(MediaResourceSearchDTO searchDTO) {
        IPage<MediaResource> page = mediaResourceService
                .listAuthorContentPage(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), searchDTO);
        return Result.ok(PageUtils.pageOf(page, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> fuzzyPage(String key, List<Integer> contentTypes, String excludeType,
            Integer current, Integer size, Long tenantId, Integer releaseType) {
        LambdaQueryWrapper<MediaResource> query = new LambdaQueryWrapper<>();
        query.eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode());
        if (StringUtils.hasText(excludeType)) {
            List<Integer> excludeTypeList = Arrays.stream(excludeType.split(",")).map(Integer::parseInt)
                    .collect(Collectors.toList());
            query.notIn(MediaResource::getContentType, excludeTypeList);
        }
        if (!CollectionUtils.isEmpty(contentTypes)) {
            query.in(MediaResource::getContentType, contentTypes);
        }
        if (tenantId > 0) {
            query.eq(MediaResource::getTenantId, tenantId);
        }
        if (Objects.nonNull(releaseType) && releaseType >= 0) {
            query.eq(MediaResource::getReleaseType, releaseType);
        }

        if (!StringUtils.isEmpty(key)) {
            query.and(wrapper -> wrapper.like(MediaResource::getShowName, key).or().eq(MediaResource::getId, key));
        }
        query.orderByDesc(MediaResource::getCreateDate);
        IPage<MediaResource> page = mediaResourceService.page(new Page<>(current, size), query);
        return Result.ok(PageUtils.pageOf(page, MediaResourceConverter::convert));
    }

    @Override
    public Result<Integer> countByJid(Long jid) {
        return Result.ok(mediaResourceService.count(Wrappers.<MediaResource>lambdaQuery()
                .eq(MediaResource::getReleaseId, jid).eq(MediaResource::getReleaseType, 1)));
    }

    @Override
    public Result<Long> editNews(MediaResourceDTO dto) {
        try {
            Long id = mediaResourceService.editNews(dto);
            // 上报到问鉴
            // wenjianService.report(id, dto.getContent());
            return Result.ok(id);
        } catch (CodeMessageException e) {
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            return Result.fail(e.getLocalizedMessage());
        }
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> queryNews(NewsQueryParam dto) {
        IPage<MediaResource> page = mediaResourceService.queryNewsPage(dto);
        return Result.ok(PageUtils.pageOf(page, MediaResourceConverter::convert));
    }

    @Override
    public Result<MediaResourceDTO> get(Long id) {
        MediaResource resource = mediaResourceService.getById(id);
        if (resource == null) {
            return Result.fail("没有内容");
        }
        MediaResourceDTO dto = MediaResourceConverter.convert(resource);
        List<MediaResourceImage> images = mediaResourceImageService.listByResourceId(id);
        if (!CollectionUtils.isEmpty(images)) {
            dto.setImages(images.stream().map(image -> new MediaResourceImageDTO(image.getType(), image.getOssId()))
                    .collect(Collectors.toList()));
        }

        // 关联的文件对象
        if (Objects.nonNull(dto.getFileId())) {
            MediaFile mediaFile = mediaFileService.getById(dto.getFileId());
            if (Objects.nonNull(mediaFile)) {
                dto.setMediaFile(MediaFileConverter.convert(mediaFile));
            }
        }
        return Result.ok(dto);
    }

    @Override
    public Result<Integer> countEnable(List<Long> mediaLst) {
        if (CollectionUtils.isEmpty(mediaLst)) {
            return Result.ok();
        }
        return Result
                .ok(mediaResourceService.count(Wrappers.<MediaResource>lambdaQuery().in(MediaResource::getId, mediaLst)
                        .eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode())));
    }

    @Override
    public Result<List<Long>> getByCondition(List<Long> jidLst, Date start, Date end) {
        if (CollectionUtils.isEmpty(jidLst) && Objects.isNull(start) && Objects.isNull(end)) {
            return Result.ok(Collections.emptyList());
        }
        List<Long> rst = mediaResourceService
                .list(Wrappers.<MediaResource>lambdaQuery()
                        .in(!CollectionUtils.isEmpty(jidLst), MediaResource::getReleaseId, jidLst)
                        .ge(Objects.nonNull(start), MediaResource::getCreateDate, start)
                        .le(Objects.nonNull(end), MediaResource::getCreateDate, end))
                .stream().map(MediaResource::getId).collect(Collectors.toList());
        return Result.ok(rst);
    }

    @Override
    public Result<List<MediaResourceImageDTO>> getImages(Long id) {
        List<MediaResourceImage> images = mediaResourceImageService.listByResourceId(id);
        if (CollectionUtils.isEmpty(images)) {
            return Result.ok(Collections.emptyList());
        }
        return Result.ok(images.stream().map(image -> new MediaResourceImageDTO(image.getType(), image.getOssId()))
                .collect(Collectors.toList()));
    }

    @Override
    public Result<MediaResourceLocationDTO> getLocation(Long id) {
        MediaResourceLocation mediaResourceLocation = mediaResourceLocationService.getByMediaId(id);
        if (Objects.isNull(mediaResourceLocation)) {
            return Result.fail("定位信息不存在");
        }
        return Result.ok(mediaResourceLocationConverter.convert(mediaResourceLocation));
    }

    @Override
    public Result<List<MediaResourceLabelDTO>> getLabels(Long id) {
        List<MediaResourceLabel> labels = mediaResourceLabelService.listByResourceId(id);
        List<MediaResourceLabelDTO> collect = labels.stream().map(e -> {
            Label label = labelService.getById(e.getLabelId());

            MediaResourceLabelDTO dto = new MediaResourceLabelDTO();
            dto.setId(e.getId());
            dto.setType(e.getType());
            dto.setResourceId(e.getResourceId());
            dto.setLabelId(e.getLabelId());
            dto.setLabelName(Objects.nonNull(label) ? label.getName() : null);
            return dto;
        }).collect(Collectors.toList());
        return Result.ok(collect);
    }

    @Override
    public Result<List<MediaResourceDTO>> bulk(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Result.ok(Collections.emptyList());
        }
        Collection<MediaResource> mediaResources = mediaResourceService.listByIds(ids);
        return Result.ok(mediaResourceConverter.convert(mediaResources));
    }

    @Override
    public Result<List<MediaResourceDTO>> baseInfoBulk(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Result.ok(Collections.emptyList());
        }
        Collection<MediaResource> mediaResources = mediaResourceService.listByIds(ids);
        return Result.ok(mediaResources.stream().map(MediaResourceConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<Long> create(MediaResourceDTO dto) {
        Long id = mediaResourceService.save(dto);
        return Result.ok(id);
    }

    @Override
    public Result<Long> createUgc(MediaUgcFileResourceDTO mediaUgcFileResourceDto) {
        Long id = mediaResourceService.saveUgc(mediaUgcFileResourceDto);
        return Result.ok(id);
    }

    @Override
    public Result<Void> updateUgc(MediaUgcFileResourceDTO mediaFileResourceDTO) {
        mediaResourceService.updateUgc(mediaFileResourceDTO);
        return Result.ok();
    }

    @Override
    public Result<Integer> ugcWorksCount(long releaseId, Boolean isIncludePrivacy) {
        LambdaQueryWrapper<MediaResource> query = Wrappers.lambdaQuery();
        query.eq(MediaResource::getReleaseId, releaseId).notIn(MediaResource::getContentType,
                Arrays.asList(ContentType.SOUND.getCode(), ContentType.PIC_FONT.getCode()));
        if (!isIncludePrivacy) {
            query.eq(MediaResource::getCanSearch, true);
            query.eq(MediaResource::getStatus, 3);
        } else {
            query.in(MediaResource::getStatus, Arrays.asList(3, 4, 5));
        }
        return Result.ok(mediaResourceService.count(query));
    }

    @Override
    public Result update(Long id, MediaResourceDTO dto) {
        dto.setId(id);
        try {
            mediaResourceService.update(dto);
            return Result.ok();
        } catch (CodeMessageException e) {
            return Result.fail(e.getMessage());
        }
    }

    @Override
    public Result updateAuditStatus(Long id, MediaResourceDTO dto) {
        try {
            mediaResourceService.updateAuditStatus(id, dto);
        } catch (CodeMessageException e) {
            return Result.fail(e.getMessage());
        }
        return Result.ok();
    }

    @Override
    public Result updateSimple(Long id, MediaResourceDTO dto) {
        dto.setId(id);
        try {
            mediaResourceService.updateSimple(dto);
            return Result.ok();
        } catch (CodeMessageException e) {
            return Result.fail(e.getMessage());
        }
    }

    @Override
    public Result<MediaResourcePendantDTO> getPendant(Long id) {
        MediaResourcePendant pendant = pendantService.getByResourceId(id);
        if (pendant != null) {
            return Result.ok(MediaResourcePendantConverter.convert(pendant));
        }
        return Result.fail("没有挂件");
    }

    @Override
    public Result<List<MediaResourcePendantDTO>> listPendantByIds(List<Long> mediaIds) {
        if (CollectionUtils.isEmpty(mediaIds)) {
            return Result.ok(Collections.emptyList());
        }
        List<MediaResourcePendant> mediaResourcePendants = pendantService.listByResourceIds(mediaIds);
        return Result.ok(mediaResourcePendants.stream().map(MediaResourcePendantConverter::convert)
                .collect(Collectors.toList()));
    }

    @Override
    public Result savePendant(Long id, MediaResourcePendantDTO dto) {
        boolean b;
        MediaResourcePendant db = pendantService.getByResourceId(id);
        if (db == null) {
            MediaResourcePendant insert = MediaResourcePendantConverter.convert(dto);
            insert.setResourceId(id);
            b = pendantService.save(insert);
        } else {
            MediaResourcePendant update = MediaResourcePendantConverter.convert(dto);
            update.setId(db.getId());
            b = pendantService.updateById(update);
        }
        if (b) {
            return Result.ok();
        } else {
            return Result.fail("保存失败");
        }
    }

    @Override
    public Result removeMediaPendant(List<Long> mediaIds) {
        LambdaQueryWrapper<MediaResourcePendant> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MediaResourcePendant::getContentId, mediaIds);
        queryWrapper.notIn(MediaResourcePendant::getResourceType, ResourceType.MATERIAL.getCode());
        if (pendantService.count(queryWrapper) > 0) {
            if (pendantService.remove(queryWrapper)) {
                return Result.fail("保存失败");
            }
        }
        return Result.ok();
    }

    @Override
    public Result<List<MediaResourceDTO>> fuzzy(List<Integer> contentTypeList, String internalName) {
        if (StringUtils.isEmpty(internalName)) {
            return Result.ok();
        }
        List<MediaResourceDTO> list = mediaResourceService.list(
                Wrappers.<MediaResource>lambdaQuery()
                        .in(MediaResource::getContentType, contentTypeList)
                        .eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode())
                        .like(MediaResource::getInternalName, internalName)
                        .and(wrapper -> wrapper.eq(MediaResource::getReleaseType, 0)
                                .or(wrapper1 -> wrapper1.eq(MediaResource::getReleaseType, 1)
                                        .eq(MediaResource::getCanSearch, 1))))
                .stream().map(MediaResourceConverter::convert).collect(Collectors.toList());
        return Result.ok(list);
    }

    @Override
    public Result<List<MediaResourceDTO>> fuzzyPptv(List<Integer> contentTypeList, String showName) {
        if (StringUtils.isEmpty(showName)) {
            return Result.ok();
        }
        List<MediaResourceDTO> list = mediaResourceService.list(
                Wrappers.<MediaResource>lambdaQuery()
                        .in(MediaResource::getContentType, contentTypeList)
                        .eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode())
                        .eq(MediaResource::getReleaseType, 2)
                        .like(MediaResource::getShowName, showName)
                        .select(MediaResource::getId, MediaResource::getShowName))
                .stream().map(MediaResourceConverter::convert).collect(Collectors.toList());
        return Result.ok(list);
    }

    @Override
    public Result<List<SysConfigDTO>> configs() {
        return Result.ok(configService.list().stream().map(sysConfig -> {
            SysConfigDTO dto = new SysConfigDTO();
            dto.setCode(sysConfig.getCode());
            dto.setName(sysConfig.getName());
            dto.setIntro(sysConfig.getIntro());
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public Result<Integer> countByCertificationId(Long certificationId) {
        return Result.ok(mediaResourceService
                .count(Wrappers.<MediaResource>lambdaQuery().eq(MediaResource::getReleaseId, certificationId)
                        .eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode())));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> getOwnMedias(long authorId, int current, int size) {
        LambdaQueryWrapper<MediaResource> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MediaResource::getReleaseId, authorId);
        return Result.ok(PageUtils.pageOf(mediaResourceService.page(new Page<>(current, size), queryWrapper),
                MediaResourceConverter::convert));
    }

    @Override
    public Result<Integer> countByColumnId(Long columnId) {
        return Result.ok(mediaResourceService
                .count(Wrappers.<MediaResource>lambdaQuery().eq(MediaResource::getColumnId, columnId)));
    }

    @Override
    public Result batchDiscard(List<Long> mediaIds) {
        LambdaUpdateWrapper<MediaResource> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(MediaResource::getId, mediaIds);
        updateWrapper.set(MediaResource::getStatus, MediaResourceStatus.DELETE.getCode());
        mediaResourceService.update(updateWrapper);
        return Result.ok();
    }

    @Override
    public Result<Void> discardUgc(Long ugcId) {
        LambdaUpdateWrapper<MediaResource> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(MediaResource::getReleaseType, 1);
        updateWrapper.eq(MediaResource::getReleaseId, ugcId);
        updateWrapper.set(MediaResource::getStatus, MediaResourceStatus.DELETE.getCode());
        mediaResourceService.update(updateWrapper);
        return Result.ok();
    }

    @Override
    public Result<Void> statusModify(Long id, Integer status) {
        mediaResourceService.updateStatus(id, status);
        return Result.ok();
    }

    @Override
    public Result<Void> deleteById(Long id) {
        mediaResourceService.removeById(id);
        return Result.ok();
    }

    @Override
    public Result<List<MediaCloudResourceDTO>> getMediaCloudResourceList(String mediaCloudIds) {
        return Result.ok(mediaResourceService.getMediaCloudResourceList(mediaCloudIds));
    }

    @Override
    public Result<List<MediaResourceDTO>> findByIdsAndContentType(FindByIdsAndContentTypeDTO dto) {
        Boolean excludeTopic = dto.getExcludeTopic();
        List<MediaIdDTO> excludeMedia;
        if (Objects.nonNull(excludeTopic) && excludeTopic) {
            // 需要筛选没有关联话题的动态
            excludeMedia = groupTopicClient.queryByWithoutTopicId();
            dto.setExcludeMediaIds(excludeMedia.stream().map(MediaIdDTO::getMediaId).collect(Collectors.toList()));
        }
        List<MediaResource> list = mediaResourceService.listRecommendContent(dto);
        return Result.ok(list.stream().map(MediaResourceConverter::convert)
                .collect(Collectors.toList()));
    }

    @Override
    public Result<Void> updateLastPassDate(Long mediaId) {
        mediaResourceService
                .update(Wrappers.<MediaResource>lambdaUpdate().set(MediaResource::getPassDate, LocalDateTime.now())
                        .eq(MediaResource::getId, mediaId));
        return Result.ok();
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> pagePosts(PostSearchDTO searchDTO) {
        LambdaQueryWrapper<MediaResource> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MediaResource::getReleaseId, searchDTO.getReleaseId());
        queryWrapper.eq(MediaResource::getAnswerId, 0);
        queryWrapper.notIn(MediaResource::getDataType, 1, 2, 4, 8);
        queryWrapper.eq(Objects.nonNull(searchDTO.getPlatform()), MediaResource::getPlatform, searchDTO.getPlatform());
        Boolean self = searchDTO.getSelf();
        if (Boolean.TRUE.equals(self)) {
            queryWrapper.in(MediaResource::getStatus, Arrays.asList(MediaResourceStatus.AUDIT.getCode(),
                    MediaResourceStatus.ENABLE.getCode(), MediaResourceStatus.HIDDEN.getCode()));
            queryWrapper.eq(MediaResource::getIsAnonymous, false);
        } else {
            queryWrapper.eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode());
            queryWrapper.eq(MediaResource::getIsAnonymous, false);
        }
        queryWrapper.orderByDesc(MediaResource::getCreateDate);
        return Result.ok(PageUtils.pageOf(
                mediaResourceService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), queryWrapper),
                MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> pageJuniorPosts(PostSearchDTO searchDTO) {
        IPage<MediaResource> mediaResourceIPage = mediaResourceService.pageJuniorPosts(searchDTO);
        return Result.ok(PageUtils.pageOf(mediaResourceIPage, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> followList(Long jid, Integer pageNum) {
        IPage<MediaResource> page = mediaResourceService.followList(new Page<>(pageNum, 10), jid);
        return Result.ok(PageUtils.pageOf(page, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> searchBroadcastByTitle(BroadcastSearchDTO searchDTO) {
        String title = searchDTO.getTitle();
        LambdaQueryWrapper<MediaResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(searchDTO.getId()), MediaResource::getId, searchDTO.getId());
        if (CollectionUtils.isEmpty(searchDTO.getContentTypes())) {
            queryWrapper.in(MediaResource::getContentType,
                    Arrays.asList(ContentType.LIVE_BROADCAST.getCode(), ContentType.INTERACTIVE_BROADCAST.getCode()));
        } else {
            queryWrapper.in(MediaResource::getContentType, searchDTO.getContentTypes());
        }
        queryWrapper.eq(MediaResource::getStatus, 3);
        queryWrapper.like(!StringUtils.isEmpty(title), MediaResource::getShowName, title);
        queryWrapper.orderByDesc(MediaResource::getCreateDate);
        Page<MediaResource> rst = mediaResourceService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()),
                queryWrapper);
        return Result.ok(PageUtils.pageOf(rst, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> liveBroadcastPage(LiveBroadcastSearchDTO searchDTO) {
        IPage<Long> page = mediaResourceService.listLiveBroadcastPage(searchDTO);
        IPage<MediaResource> mediaResourceIPage = new Page<>(searchDTO.getCurrent(), searchDTO.getSize());
        log.info("liveBroadcastPage result : {}", JSON.toJSONString(page.getRecords()));
        if (Objects.nonNull(page)) {
            mediaResourceIPage.setTotal(page.getTotal());
            mediaResourceIPage.setSize(page.getSize());
            mediaResourceIPage.setPages(page.getPages());
            mediaResourceIPage.setCurrent(page.getCurrent());
            if (!CollectionUtils.isEmpty(page.getRecords())) {
                List<Long> mediaIds = page.getRecords();
                List<MediaResource> mediaResourceList = mediaResourceService.listByIds(mediaIds);
                // 因数据库in语法无法按照传入数组顺序排序，故需要在逻辑中进行排序
                Map<Long, MediaResource> rstMap = mediaResourceList.stream()
                        .collect(Collectors.toMap(MediaResource::getId, Function.identity()));
                mediaResourceIPage.setRecords(mediaIds.stream().map(rstMap::get).collect(Collectors.toList()));
            }
        } else {
            mediaResourceIPage.setTotal(0);
            mediaResourceIPage.setPages(0);
        }
        return Result.ok(PageUtils.pageOf(mediaResourceIPage, MediaResourceConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> searchBroadcastByTitleAndType(BroadcastSearchDTO searchDTO,
            Integer contentType) {
        String title = searchDTO.getTitle();
        LambdaQueryWrapper<MediaResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MediaResource::getContentType, contentType);
        queryWrapper.eq(MediaResource::getStatus, 3);
        queryWrapper.like(!StringUtils.isEmpty(title), MediaResource::getShowName, title);
        queryWrapper.orderByDesc(MediaResource::getCreateDate);
        Page<MediaResource> rst = mediaResourceService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()),
                queryWrapper);
        return Result.ok(PageUtils.pageOf(rst, MediaResourceConverter::convert));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> changeType(Long id, Integer type) {
        mediaResourceService.update(Wrappers.<MediaResource>lambdaUpdate().set(MediaResource::getPlayStyle, type)
                .eq(MediaResource::getId, id));
        return Result.ok();
    }

    @Override
    public Result<PageDTO<MediaResourceDTO>> queryByCursor(String cursor) {
        int pageNum = StringUtils.isEmpty(cursor) ? 1 : Integer.parseInt(cursor);
        // 竖视频瀑布流默认不查询提问资源
        Page<MediaResource> rst = mediaResourceService.page(new Page<>(pageNum, 10),
                Wrappers.<MediaResource>lambdaQuery()
                        .eq(MediaResource::getContentType, 1).eq(MediaResource::getPlayStyle, 2)
                        .eq(MediaResource::getAnswerId, 0)
                        .eq(MediaResource::getStatus, MediaResourceStatus.ENABLE.getCode())
                        .orderByDesc(MediaResource::getCreateDate));
        return Result.ok(PageUtils.pageOf(rst, MediaResourceConverter::convert));
    }

    @Override
    public Result<List<MediaResourceDTO>> fetchMediaByIds(Collection<Long> mediaIds) {
        return Result.ok(mediaResourceService.listByIds(mediaIds).stream().map(MediaResourceConverter::convert)
                .collect(Collectors.toList()));
    }

    @Override
    public Result<List<MediaResourceExternalDTO>> fetchGanyunRelateByMediaIds(List<Long> mediaIds) {
        LambdaQueryWrapper<MediaResourceExternal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MediaResourceExternal::getMediaId, mediaIds);
        List<MediaResourceExternal> list = mediaResourceExternalContentService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(Collections.emptyList());
        } else {
            return Result.ok(list.stream().map(entity -> {
                MediaResourceExternalDTO dto = new MediaResourceExternalDTO();
                dto.setContentId(entity.getContentId());
                dto.setLinkTitle(entity.getLinkTitle());
                dto.setLinkUrl(entity.getLinkUrl());
                dto.setMediaId(entity.getMediaId());
                return dto;
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public Result<List<Long>> fetchMediaReleaseIds(Integer type) {
        return Result.ok(mediaResourceService.listReleaseIdByType(type));
    }

    @Override
    public Result<List<MediaResourceDTO>> listRelateByContentId(List<Long> contentIds) {
        log.info("listRelateByContentId start contentIds is {}", contentIds);
        if (CollectionUtils.isEmpty(contentIds)) {
            return Result.ok(Collections.emptyList());
        }

        LambdaQueryWrapper<MediaResourceExternal> relateQuery = Wrappers.lambdaQuery();
        relateQuery.in(MediaResourceExternal::getContentId, contentIds);
        List<MediaResourceExternal> relateList = mediaResourceExternalContentService.list(relateQuery);

        Map<Long, MediaResourceExternal> relateMap = relateList.stream()
                .collect(Collectors.toMap(MediaResourceExternal::getMediaId, e -> e, (e1, e2) -> e2));
        if (CollectionUtils.isEmpty(relateMap.keySet())) {
            return Result.ok(Collections.emptyList());
        }

        LambdaQueryWrapper<MediaResource> mediaQuery = new LambdaQueryWrapper<>();
        mediaQuery.select(MediaResource::getId, MediaResource::getStatus);
        mediaQuery.in(MediaResource::getId, relateMap.keySet());
        List<MediaResource> mediaResourceList = mediaResourceService.list(mediaQuery);

        List<MediaResourceDTO> collect = mediaResourceList.stream().map(entity -> {
            MediaResourceDTO dto = new MediaResourceDTO();
            dto.setId(entity.getId());
            dto.setContentId(relateMap.get(entity.getId()).getContentId());
            dto.setStatus(entity.getStatus());
            return dto;
        }).collect(Collectors.toList());

        return Result.ok(collect);
    }

    @Override
    public Result<Void> updateAlgorithmLabel(List<MediaResourceDTO> mediaResourceDTOList) {
        try {
            mediaResourceService.updateAlgorithmLabel(mediaResourceDTOList);
            return Result.ok();
        } catch (CodeMessageException e) {
            return Result.fail(e.getMessage());
        }
    }

    @Override
    public Result<Boolean> readAnswer(List<MediaResourceDTO> mediaResourceDTOList) {
        return Result.ok(mediaResourceService.readAnswer(mediaResourceDTOList));
    }

    @Override
    public Result<AnswerReminderPopUpDTO> getAnswerReminderByUserId(Long userId) {
        AnswerReminderPopUpDTO answerReminderPopUpDTO = mediaResourceService.getAnswerReminderByUserId(userId);
        return Result.ok(answerReminderPopUpDTO);
    }

    @Override
    public Result<Long> createTodayTalk(MediaResourceDTO dto) {
        try {
            Long id = mediaResourceService.saveTodayTalk(dto);
            return Result.ok(id);
        } catch (CodeMessageException e) {
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            return Result.fail(e.getLocalizedMessage());
        }
    }

    @Override
    public Result<MediaResourceDTO> simpleQueryById(Long id) {

        LambdaQueryWrapper<MediaResource> queryWrapper = Wrappers.lambdaQuery();
        // 排除指定列
        queryWrapper.select(MediaResource.class, info -> !info.getColumn().equals("content"));
        queryWrapper.eq(MediaResource::getId, id);
        MediaResource resource = mediaResourceService.getOne(queryWrapper);
        if (resource == null) {
            return Result.ok(null);
        }
        MediaResourceDTO dto = MediaResourceConverter.convert(resource);
        return Result.ok(dto);
    }

    @Override
    public Result<List<MediaResourceLabelDTO>> batchGetLabelsByIds(List<Long> ids) {
        List<MediaResourceLabel> list = mediaResourceLabelService.list(Wrappers.lambdaQuery(MediaResourceLabel.class)
                .in(MediaResourceLabel::getResourceId, ids));
        List<Long> labelIds = list.stream().map(MediaResourceLabel::getLabelId).distinct().collect(Collectors.toList());
        List<Label> labelList = labelService.listByIds(labelIds);
        Map<Long, String> labelMap = labelList.stream().collect(Collectors.toMap(Label::getId, Label::getName,(u1,u2)->u2));

        return Result.ok(list.stream().map(e -> {
            MediaResourceLabelDTO dto = new MediaResourceLabelDTO();
            dto.setId(e.getId());
            dto.setType(e.getType());
            dto.setResourceId(e.getResourceId());
            dto.setLabelId(e.getLabelId());
            dto.setLabelName(labelMap.get(e.getLabelId()));
            return dto;
        }).collect(Collectors.toList()));
    }
}
