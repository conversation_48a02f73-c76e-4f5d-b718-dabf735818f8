package com.jxntv.gvideo.web.controller.be.converter.race;

import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.RaceActivityClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityConfig;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceActivityConfigVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceActivityVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceLiveBroadcastVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceScheduleVO;
import com.jxntv.gvideo.web.controller.be.model.vo.race.RaceTeamVO;
import lombok.extern.slf4j.Slf4j;

/**
 * 球赛活动转换器 - Web端管理 用于VO和DTO之间的数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RaceActivityConverter {

    @Resource
    private RaceActivityClient raceActivityClient;

    @Resource
    private OssClient ossClient;

    @Resource
    private MediaResourceClient mediaResourceClient;
    // ==================== RaceActivity 转换方法 ====================

    /**
     * RaceActivityVO转RaceActivityDTO
     *
     * @param vo VO对象
     * @return DTO对象
     */
    public RaceActivityDTO convertToDto(RaceActivityVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        RaceActivityDTO dto = new RaceActivityDTO();
        dto.setId(vo.getId());
        dto.setTitle(vo.getTitle());
        dto.setIntroduction(vo.getIntroduction());
        dto.setStartTime(vo.getStartTime());
        dto.setEndTime(vo.getEndTime());
        dto.setStatus(vo.getStatus());
        if (Objects.nonNull(vo.getConfig())) {
            RaceActivityConfig config = new RaceActivityConfig();
            config.setScoreOssId(vo.getConfig().getScoreOssId());
            dto.setConfig(config);
        }
        return dto;
    }

    /**
     * RaceActivityDTO转RaceActivityVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public RaceActivityVO convertToVo(RaceActivityDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceActivityVO vo = new RaceActivityVO();
        vo.setId(dto.getId());
        vo.setTitle(dto.getTitle());
        vo.setIntroduction(dto.getIntroduction());
        vo.setStartTime(dto.getStartTime());
        vo.setEndTime(dto.getEndTime());
        vo.setStatus(dto.getStatus());
        vo.setStatusText(getStatusText(dto.getStatus()));
        if (Objects.nonNull(dto.getConfig())) {
            RaceActivityConfigVO config = new RaceActivityConfigVO();
            config.setScoreOssId(dto.getConfig().getScoreOssId());

            if (StringUtils.hasText(dto.getConfig().getScoreOssId())) {
                OssDTO ossDTO = ossClient.getOssFile(dto.getConfig().getScoreOssId()).getResult();
                if (Objects.nonNull(ossDTO)) {
                    config.setScoreOssUrl(ossDTO.getUrl());
                }
            }

            vo.setConfig(config);
        }

        vo.setCreateDate(dto.getCreateDate());
        vo.setUpdateDate(dto.getUpdateDate());
        return vo;
    }



    // ==================== RaceTeam 转换方法 ====================

    /**
     * RaceTeamVO转RaceTeamDTO
     *
     * @param vo VO对象
     * @return DTO对象
     */
    public RaceTeamDTO convertTeamToDto(RaceTeamVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        RaceTeamDTO dto = new RaceTeamDTO();
        dto.setId(vo.getId());
        dto.setActivityId(vo.getActivityId());
        dto.setTitle(vo.getTitle());
        dto.setIntroduction(vo.getIntroduction());
        dto.setIcon(vo.getIcon());
        dto.setCreateDate(vo.getCreateDate());
        dto.setUpdateDate(vo.getUpdateDate());
        return dto;
    }

    /**
     * RaceTeamDTO转RaceTeamVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public RaceTeamVO convertTeamToVo(RaceTeamDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceTeamVO vo = new RaceTeamVO();
        vo.setId(dto.getId());
        vo.setActivityId(dto.getActivityId());
        vo.setTitle(dto.getTitle());
        vo.setIntroduction(dto.getIntroduction());
        vo.setIcon(dto.getIcon());

        if (StringUtils.hasText(dto.getIcon())) {
            vo.setIconUrl(ossClient.getOssFile(dto.getIcon()).map(OssDTO::getUrl).orElse(""));
        }
        vo.setCreateDate(dto.getCreateDate());
        vo.setUpdateDate(dto.getUpdateDate());
        return vo;
    }



    // ==================== RaceSchedule 转换方法 ====================

    /**
     * RaceScheduleVO转RaceScheduleDTO
     *
     * @param vo VO对象
     * @return DTO对象
     */
    public RaceScheduleDTO convertScheduleToDto(RaceScheduleVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        RaceScheduleDTO dto = new RaceScheduleDTO();
        dto.setId(vo.getId());
        dto.setActivityId(vo.getActivityId());
        dto.setTitle(vo.getTitle());
        dto.setIntroduction(vo.getIntroduction());
        dto.setStartTime(vo.getStartTime());
        dto.setEndTime(vo.getEndTime());
        dto.setHomeTeamId(vo.getHomeTeamId());
        dto.setAwayTeamId(vo.getAwayTeamId());
        dto.setHomeTeamScore(vo.getHomeTeamScore());
        dto.setAwayTeamScore(vo.getAwayTeamScore());
        dto.setResourceId(vo.getResourceId());
        dto.setPushTitle(vo.getPushTitle());
        dto.setPushContent(vo.getPushContent());
        dto.setStatus(vo.getStatus());
        dto.setCreateDate(vo.getCreateDate());
        dto.setUpdateDate(vo.getUpdateDate());
        dto.setHomeTeamCheer(vo.getHomeTeamCheer());
        dto.setAwayTeamCheer(vo.getAwayTeamCheer());
        return dto;
    }

    /**
     * RaceScheduleDTO转RaceScheduleVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public RaceScheduleVO convertScheduleToVo(RaceScheduleDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceScheduleVO vo = new RaceScheduleVO();
        vo.setId(dto.getId());
        vo.setActivityId(dto.getActivityId());
        vo.setTitle(dto.getTitle());
        vo.setIntroduction(dto.getIntroduction());
        vo.setStartTime(dto.getStartTime());
        vo.setEndTime(dto.getEndTime());
        vo.setHomeTeamId(dto.getHomeTeamId());
        vo.setAwayTeamId(dto.getAwayTeamId());
        vo.setHomeTeamScore(dto.getHomeTeamScore());
        vo.setAwayTeamScore(dto.getAwayTeamScore());
        vo.setResourceId(dto.getResourceId());
        vo.setPushTitle(dto.getPushTitle());
        vo.setPushContent(dto.getPushContent());
        vo.setStatus(dto.getStatus());
        vo.setStatusText(getStatusText(dto.getStatus()));
        vo.setCreateDate(dto.getCreateDate());
        vo.setUpdateDate(dto.getUpdateDate());
        vo.setHomeTeamCheer(dto.getHomeTeamCheer());
        vo.setAwayTeamCheer(dto.getAwayTeamCheer());

        // 查询主队名称
        if (Objects.nonNull(vo.getHomeTeamId())) {
            RaceTeamDTO raceTeamDTO = raceActivityClient.getTeamById(vo.getHomeTeamId()).orElse(null);
            if (Objects.nonNull(raceTeamDTO)) {
                vo.setHomeTeamName(raceTeamDTO.getTitle());
                if (StringUtils.hasText(raceTeamDTO.getIcon())) {
                    vo.setHomeTeamIcon(ossClient.getOssFile(raceTeamDTO.getIcon()).map(OssDTO::getUrl).orElse(""));
                }
            }

        }

        // 查询客队名称
        if (Objects.nonNull(vo.getAwayTeamId())) {
            RaceTeamDTO raceTeamDTO = raceActivityClient.getTeamById(vo.getAwayTeamId()).orElse(null);
            if (Objects.nonNull(raceTeamDTO)) {
                vo.setAwayTeamName(raceTeamDTO.getTitle());
                if (StringUtils.hasText(raceTeamDTO.getIcon())) {
                    vo.setAwayTeamIcon(ossClient.getOssFile(raceTeamDTO.getIcon()).map(OssDTO::getUrl).orElse(""));
                }
            }
        }
        vo.setResourceTitle(Optional.ofNullable(dto.getResourceId()).map(e -> mediaResourceClient.get(e)).map(Result::getResult).map(MediaResourceDTO::getShowName).orElse(""));
        return vo;
    }



    // ==================== RaceLiveBroadcast 转换方法 ====================

    /**
     * RaceLiveBroadcastVO转RaceLiveBroadcastDTO
     *
     * @param vo VO对象
     * @return DTO对象
     */
    public RaceLiveBroadcastDTO convertLiveBroadcastToDto(RaceLiveBroadcastVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        RaceLiveBroadcastDTO dto = new RaceLiveBroadcastDTO();
        dto.setId(vo.getId());
        dto.setActivityId(vo.getActivityId());
        dto.setResourceId(vo.getResourceId());
        dto.setTitle(vo.getTitle());
        dto.setStartTime(vo.getStartTime());
        dto.setEndTime(vo.getEndTime());
        dto.setCreateUserId(vo.getCreateUserId());
        dto.setCreateUserName(vo.getCreateUserName());
        dto.setCreateDate(vo.getCreateDate());
        dto.setUpdateUserId(vo.getUpdateUserId());
        dto.setUpdateUserName(vo.getUpdateUserName());
        dto.setUpdateDate(vo.getUpdateDate());
        return dto;
    }

    /**
     * RaceLiveBroadcastDTO转RaceLiveBroadcastVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public RaceLiveBroadcastVO convertLiveBroadcastToVo(RaceLiveBroadcastDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceLiveBroadcastVO vo = new RaceLiveBroadcastVO();
        vo.setId(dto.getId());
        vo.setActivityId(dto.getActivityId());
        vo.setResourceId(dto.getResourceId());
        vo.setTitle(dto.getTitle());
        vo.setStartTime(dto.getStartTime());
        vo.setEndTime(dto.getEndTime());
        vo.setCreateUserId(dto.getCreateUserId());
        vo.setCreateUserName(dto.getCreateUserName());
        vo.setCreateDate(dto.getCreateDate());
        vo.setUpdateUserId(dto.getUpdateUserId());
        vo.setUpdateUserName(dto.getUpdateUserName());
        vo.setUpdateDate(dto.getUpdateDate());
        vo.setResourceTitle(Optional.ofNullable(dto.getResourceId()).map(e -> mediaResourceClient.get(e)).map(Result::getResult).map(MediaResourceDTO::getShowName).orElse(""));
        return vo;
    }

    // ==================== 工具方法 ====================

    /**
     * 获取状态文本
     *
     * @param status 状态码
     * @return 状态文本
     */
    private String getStatusText(Integer status) {
        if (Objects.isNull(status)) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "未开始";
            case 1:
                return "进行中";
            case 2:
                return "已结束";
            default:
                return "未知";
        }
    }
}
