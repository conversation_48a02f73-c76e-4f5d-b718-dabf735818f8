package com.jxntv.gvideo.app.be.service.impl;

import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.converter.TvConverter;
import com.jxntv.gvideo.app.be.converter.feed.FeedV2Converter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.manager.*;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.model.vo.tv.*;
import com.jxntv.gvideo.app.be.service.TvService;
import com.jxntv.gvideo.app.be.utils.LocalDateTimeUtils;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.newscast.JxntvContentDTO;
import com.jxntv.gvideo.media.client.dto.newscast.JxntvContentRequest;
import com.jxntv.gvideo.media.client.dto.newscast.JxntvContentResponse;
import com.jxntv.gvideo.media.client.dto.tv.*;
import com.jxntv.gvideo.media.client.enums.EnableEnum;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexDTO;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexSearchDTO;
import com.microsoft.schemas.office.visio.x2012.main.PagesDocument;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/30
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class TvServiceImpl implements TvService {
    @Resource
    private TvConverter tvConverter;
    @Resource
    private TvChannelManager tvChannelManager;
    @Resource
    private TvColumnManager tvColumnManager;
    @Resource
    private TvProgramManager tvProgramManager;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private FeedV2Converter feedV2Converter;

    @Resource
    private MediaResourceIndexSearchManager mediaResourceIndexSearchManager;


    @Override
    public Page<TvChannelListVO> getChannelPage(Integer pageNum, Integer pageSize, List<Integer> type, Integer source) {

        TvChannelSearchDTO searchDTO = new TvChannelSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setTypes(type);
        searchDTO.setSource(source);
        searchDTO.setStatus(EnableEnum.ENABLE.getCode());

        PageDTO<TvChannelDTO> page = tvChannelManager.page(searchDTO).orElse(PageDTO.empty(pageNum, pageSize));
        // 展示全部节目时tab去除 陶瓷频道、风尚购物
        if (!CollectionUtils.isEmpty(type) && "0,2".equals(type.stream().map(String::valueOf).collect(Collectors.joining(",")))) {
            Set<String> channelNameSet = new HashSet<>(Arrays.asList("陶瓷频道", "风尚购物"));
            page.setList(page.getList().stream().filter(e -> !channelNameSet.contains(e.getChannelName())).collect(Collectors.toList()));
        }

        return Page.Of(page, tvConverter::toListVO);
    }

    @Override
    public TvChannelVO getChannelById(Long channelId) {
        return tvChannelManager.getById(channelId).map(e -> tvConverter.convert(e)).orElse(null);
    }

    @Override
    public TvColumnVO getColumnById(Long columnId) {
        Result<TvColumnDTO> result = tvColumnManager.getById(columnId);
        if (!result.callSuccess() || Objects.isNull(result.getResult())) {
            return null;
        }
        return result.map(this.tvConverter::convert).orElse(null);
    }

    @Override
    public List<TvChannelMenuVO> listChannelMenus(Long channelId, String playDate, Long jid) {
        List<TvChannelMenuDTO> menus = tvChannelManager.listMenus(channelId, playDate).orElse(Collections.emptyList());
        return menus.stream().map(e -> tvConverter.convert(e)).peek(tvChannelMenuVO -> {
            String time = tvChannelMenuVO.getPlayTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String key = "tv::program::" + tvChannelMenuVO.getProgramName() + "::" + time;
            List<String> list = stringRedisTemplate.opsForList().range(key, 0, -1);
            if (!CollectionUtils.isEmpty(list)) {
                tvChannelMenuVO.setHasSubscribe(list.contains(String.valueOf(jid)));
            }
        }).collect(Collectors.toList());
    }

    @Override
    public Page<TvColumnListVO> getChannelColumnPage(Long channelId, Integer pageNum, Integer pageSize) {
        PageDTO<TvColumnDTO> page = tvChannelManager.getChannelColumnPage(channelId, pageNum, pageSize).orElse(PageDTO.empty(pageNum, pageSize));
        return Page.Of(page, tvConverter::convertSimple);
    }

    @Override
    public Page<TvProgramVO> getHotProgramPage(Integer pageNum, Integer pageSize) {
        PageDTO<TvHotProgramDTO> page = tvProgramManager.getHotProgramPage(pageNum, pageSize).orElseThrow();
        return Page.Of(page, item -> tvProgramManager.getById(item.getProgramId()).map(e -> tvConverter.convert(e)).orElse(null));
    }

    @Override
    public Page<TvProgramFeedVO> getProgramPage(Long columnId, Long programId, Integer pageNum, Integer pageSize) {
        return getPrograms(columnId, programId, null, null, pageNum, pageSize);
    }

    @Override
    public TvProgramPageV2VO getProgramPageV2(Long columnId, Long programId, Integer year, Integer month, Integer pageNum, Integer pageSize) {

        //  查询播放日期，构建时间筛选
        List<TvTimeSelector> timeSelectors = getTimeSelectors(columnId);

        //  查询栏目对应的节目列表
        Page<TvProgramFeedVO> programPage = getPrograms(columnId, programId, year, month, pageNum, pageSize);

        TvProgramPageV2VO result = new TvProgramPageV2VO();
        result.setTimeSelectors(timeSelectors);
        result.setPrograms(programPage);
        return result;
    }

    @Override
    public Result<Boolean> subscribe(TvProgramSubscribeVO vo) {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail(CodeMessage.TOKEN_EXPIRED.getMessage());
        }
        String time = vo.getPlayTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String key = "tv::program::" + vo.getProgramName() + "::" + time;
        stringRedisTemplate.opsForList().leftPush(key, String.valueOf(jid));
        return Result.ok();
    }

    /**
     * 如果包含拆条展示拆条，否则展示栏目分页列表
     * 1、拆条列表
     * 2、当前频道栏目分页列表
     *
     * @param programId 节目ID
     * @param pageNum   页号
     * @param pageSize  分页大小
     * @return 拆条或者栏目列表
     */

    @Override
    public Map<String, Object> getSplitPage(Long programId, Long mediaId, Integer pageNum, Integer pageSize) {
        //  节目信息
        TvProgramDTO programDTO = tvProgramManager.getByMediaId(programId).orElseThrow(() -> new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "节目不存在"));
        String programName = programDTO.getProgramName();

        Map<String, String> stringStringMap = parseProgramName(programName);
        String title = stringStringMap.get("title");
        String date = stringStringMap.get("date");
        String time = stringStringMap.get("time");

        LocalDateTime dateStart;
        LocalDateTime dateEnd;
        // 拆条列表
        try {
            if (StringUtils.hasText(time)) {
                //  如果是整点拆条，取最近2小时之内的拆条
                dateStart = LocalDateTime.parse(date + " " + time, DateTimeFormatter.ofPattern("yyyyMMdd HH:mm"));
                dateEnd = dateStart.plusHours(2);
            } else {
                //  如果是整天拆条，取1天之内的拆条
                dateStart = LocalDateTime.parse(date + " 00:00", DateTimeFormatter.ofPattern("yyyyMMdd HH:mm"));
                dateEnd = dateStart.plusDays(1);
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期时间格式错误: " + date + (StringUtils.hasText(time) ? " " + time : ""), e);
        }

        MediaResourceIndexSearchDTO.LabelFilter labelFilter = new MediaResourceIndexSearchDTO.LabelFilter();
        labelFilter.setType(2);
        labelFilter.setName(title);

        MediaResourceIndexSearchDTO searchDTO = new MediaResourceIndexSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setLabelFilters(Collections.singletonList(labelFilter));
        searchDTO.setStatus(Collections.singletonList(3));
        searchDTO.setStartTime(LocalDateTimeUtils.ms(dateStart));
        searchDTO.setEndTime(LocalDateTimeUtils.ms(dateEnd));

        PageDTO<MediaResourceIndexDTO> response = mediaResourceIndexSearchManager.search(searchDTO).orElse(PageDTO.empty());
        List<MediaResourceIndexDTO> data = response.getList();
        List<FeedVO> collect = data.stream().parallel()
                .map(e -> feedV2Converter.convert(FeedContext.builder()
                        .id(Long.valueOf(e.getId()))
                        .jid(ThreadLocalCache.getJid())
                        .build()))
                .collect(Collectors.toList());

        List<Object> objects = new ArrayList<>(collect);

        if (!CollectionUtils.isEmpty(collect)) {
            if (Objects.equals(pageNum, 1)) {

                //  整期放第一个元素
                TvColumnDTO columnDTO = tvColumnManager.getById(programDTO.getColumnId()).orElseThrow(() -> new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "栏目不存在"));

                TvProgramFeedVO programFeedVO = tvConverter.convert(columnDTO, programDTO);

                for (FeedVO vo : collect) {
                    if (Objects.nonNull(programFeedVO.getGroupInfo())) {
                        vo.setGroupInfo(programFeedVO.getGroupInfo());
                    }
                    if (Objects.nonNull(programFeedVO.getAuthor())) {
                        vo.setAuthor(programFeedVO.getAuthor());
                    }
                }

                objects.add(0, programFeedVO);

                //  插入拆条
                if (Objects.nonNull(mediaId)) {
                    //  先移除该拆条
                    objects.removeIf(e -> e instanceof FeedVO && Objects.equals(((FeedVO) e).getId(), mediaId));

                    //  放在第2个位置
                    FeedVO convert = feedV2Converter.convert(FeedContext.builder()
                            .id(mediaId)
                            .jid(ThreadLocalCache.getJid())
                            .build());
                    if (Objects.nonNull(convert)) {
                        objects.add(1, convert);
                    }
                }

            } else {
                objects.removeIf(e -> e instanceof FeedVO && Objects.equals(((FeedVO) e).getId(), mediaId));
            }
        }


        Map<String, Object> map = new HashMap<>();
        map.put("tabName", "本期看点");
        map.put("pageNum", pageNum);
        map.put("pageSize", pageSize);
        map.put("hasMore", response.getTotal() > response.getPageNum() * response.getPageSize());
        map.put("list", objects);
        return map;

    }

    @Override
    public Map<String, Object> getColumnSplitPage(Long columnId, String date, Integer pageNum, Integer pageSize) {
        //  节目信息
        TvColumnDTO columnDTO = tvColumnManager.getById(columnId).orElseThrow(() -> new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "栏目不存在"));
        LocalDateTime dateStart = LocalDateTime.of(LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd")), LocalTime.MIN);
        LocalDateTime dateEnd = dateStart.plusDays(1);

        MediaResourceIndexSearchDTO.LabelFilter labelFilter = new MediaResourceIndexSearchDTO.LabelFilter();
        labelFilter.setType(2);
        labelFilter.setName(columnDTO.getColumnName());


        MediaResourceIndexSearchDTO request = new MediaResourceIndexSearchDTO();
        request.setCurrent(pageNum);
        request.setSize(pageSize);
        request.setLabelFilters(Collections.singletonList(labelFilter));
        request.setStartTime(LocalDateTimeUtils.ms(dateStart));
        request.setEndTime(LocalDateTimeUtils.ms(dateEnd));

        PageDTO<MediaResourceIndexDTO> response = mediaResourceIndexSearchManager.search(request).orElse(PageDTO.empty());
        List<MediaResourceIndexDTO> data = response.getList();
        List<FeedVO> collect = data.stream().parallel()
                .map(e -> feedV2Converter.convert(FeedContext.builder()
                        .id(Long.valueOf(e.getId()))
                        .jid(ThreadLocalCache.getJid())
                        .build()))
                .collect(Collectors.toList());


        TvProgramSearchDTO programSearchDTO = new TvProgramSearchDTO();
        programSearchDTO.setColumnId(columnId);
        programSearchDTO.setPlayDateStart(dateStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        programSearchDTO.setPlayDateEnd(dateStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        programSearchDTO.setCurrent(1);
        programSearchDTO.setSize(1);

        List<TvProgramDTO> dtoList = tvProgramManager.page(programSearchDTO).map(PageDTO::getList).orElse(null);
        if (!CollectionUtils.isEmpty(dtoList)) {
            TvProgramDTO dto = dtoList.get(0);
            collect.forEach(e -> e.setProgramId(dto.getMediaId()));
        }

        Map<String, Object> map = new HashMap<>();
        map.put("tabName", "本期看点");
        map.put("pageNum", pageNum);
        map.put("pageSize", pageSize);
        map.put("hasMore", response.getTotal() > response.getPageNum() * response.getPageSize());
        map.put("list", collect);
        return map;

    }


    private Map<String, String> parseProgramName(String input) {
        // 增强版正则：时间字段变为可选匹配
        String regex = "^(.*?)(\\d{8})(?:\\s(\\d{1,2}:\\d{2}))?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String title = matcher.group(1).trim();
            String date = matcher.group(2);
            String time = matcher.group(3); // 可能为null

            Map<String, String> map = new HashMap<>();
            map.put("title", title);
            map.put("date", date);
            map.put("time", time);

            return map;
        } else {
            return Collections.emptyMap();
        }

    }


    private Page<TvProgramFeedVO> getPrograms(Long columnId, Long programId, Integer year, Integer month, Integer pageNum, Integer pageSize) {
        //  获取栏目信息
        TvColumnDTO column = tvColumnManager.getById(columnId).orElseThrow();

        TvProgramSearchDTO searchDTO = new TvProgramSearchDTO();
        searchDTO.setColumnId(columnId);
        searchDTO.setAnchorId(programId);
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setStatus(EnableEnum.ENABLE.getCode());

        //  设置播放时间
        if (Objects.nonNull(year)) {
            if (Objects.nonNull(month) && month > 0 && month < 13) {
                LocalDate localDate = LocalDate.of(year, month, 1);
                int lengthOfMonth = localDate.lengthOfMonth();
                if (month < 10) {
                    searchDTO.setPlayDateStart(year + "-0" + month + "-01");
                    searchDTO.setPlayDateEnd(year + "-0" + month + "-" + lengthOfMonth);
                } else {
                    searchDTO.setPlayDateStart(year + "-" + month + "-01");
                    searchDTO.setPlayDateEnd(year + "-" + month + "-" + lengthOfMonth);
                }
            } else {
                searchDTO.setPlayDateStart(year + "-01-01");
                searchDTO.setPlayDateEnd(year + "-12-31");
            }
        }


        PageDTO<TvProgramDTO> page = tvProgramManager.page(searchDTO).orElseThrow();
        return Page.Of(page, program -> tvConverter.convert(column, program));
    }

    private List<TvTimeSelector> getTimeSelectors(Long columnId) {
        List<String> playDateList = tvProgramManager.getPlayDateList(columnId).orElseThrow()
                .stream()
                .filter(StringUtils::hasText)
                .filter(s -> !"0000-00-00".equals(s))
                .sorted(Comparator.comparing(Object::toString).reversed())
                .collect(Collectors.toList());
        Map<String, Set<String>> dateGroup = new LinkedHashMap<>();
        for (String playDate : playDateList) {
            String[] dateNums = playDate.split("-");
            String playYear = dateNums[0];
            String playMonth = dateNums[1];

            if (dateGroup.containsKey(playYear)) {
                Set<String> monthList = dateGroup.get(playYear);
                monthList.add(playMonth);
            } else {
                Set<String> monthList = new LinkedHashSet<>();
                monthList.add(playMonth);
                dateGroup.put(playYear, monthList);
            }
        }
        return dateGroup.entrySet().stream().map(e -> {
            String key = e.getKey();
            Set<String> value = e.getValue();
            TvTimeSelector selector = new TvTimeSelector();
            selector.setType(0);
            selector.setValue(Integer.parseInt(key));

            List<TvTimeSelector> children = value.stream().map(s -> {
                TvTimeSelector child = new TvTimeSelector();
                child.setType(1);
                child.setValue(Integer.parseInt(s));
                child.setChildren(Collections.emptyList());
                return child;
            }).collect(Collectors.toList());

            selector.setChildren(children);

            return selector;

        }).collect(Collectors.toList());
    }
}
