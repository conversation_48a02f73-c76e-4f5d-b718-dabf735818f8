package com.jxntv.gvideo.web.controller.be.model.vo.race;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 球赛赛程VO - Web端管理
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RaceScheduleVO", description = "球赛赛程VO")
public class RaceScheduleVO {

    @ApiModelProperty(value = "赛程ID")
    private Long id;

    @ApiModelProperty(value = "活动ID", required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    @ApiModelProperty(value = "比赛标题（轮次）", required = true)
    @NotBlank(message = "比赛标题不能为空")
    private String title;

    @ApiModelProperty(value = "比赛活动简介")
    private String introduction;

    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "主队ID", required = true)
    @NotNull(message = "主队ID不能为空")
    private Long homeTeamId;

    @ApiModelProperty(value = "客队ID", required = true)
    @NotNull(message = "客队ID不能为空")
    private Long awayTeamId;

    @ApiModelProperty(value = "主队名称")
    private String homeTeamName;

    @ApiModelProperty(value = "主队icon")
    private String homeTeamIcon;

    @ApiModelProperty(value = "客队名称")
    private String awayTeamName;

    @ApiModelProperty(value = "客队icon")
    private String awayTeamIcon;

    @ApiModelProperty(value = "主队得分")
    private Integer homeTeamScore;

    @ApiModelProperty(value = "客队得分")
    private Integer awayTeamScore;

    @ApiModelProperty(value = "主队助威数（初始值）")
    private Integer homeTeamCheer;

    @ApiModelProperty(value = "客队助威数（初始值）")
    private Integer awayTeamCheer;

    @ApiModelProperty(value = "资源ID")
    private Long resourceId;

    @ApiModelProperty(value = "资源标题")
    private String resourceTitle;

    @ApiModelProperty(value = "预约推送标题")
    private String pushTitle;

    @ApiModelProperty(value = "预约推送内容")
    private String pushContent;

    @ApiModelProperty(value = "状态：0-未开始、1-进行中、2-已结束")
    private Integer status;

    @ApiModelProperty(value = "状态文本")
    private String statusText;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateDate;
}
