package com.jxntv.gvideo.app.be.converter.race;

import java.time.ZoneId;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.converter.feed.FeedV2Converter;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.manager.RaceManager;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceActivityConfigVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceActivityVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceLiveBroadcastVO;
import com.jxntv.gvideo.app.be.model.vo.race.RaceScheduleVO;
import com.jxntv.gvideo.common.constants.RedisConstants;
import com.jxntv.gvideo.media.client.dto.race.RaceActivityDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceLiveBroadcastDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceTeamDTO;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * 球赛活动转换器 用于VO和DTO之间的数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RaceActivityConverter {

    @Resource
    private RaceManager raceManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private FeedV2Converter feedV2Converter;

    public RaceActivityVO convert(RaceActivityDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        RaceActivityVO vo = new RaceActivityVO();
        vo.setId(dto.getId());
        vo.setTitle(dto.getTitle());
        vo.setIntroduction(dto.getIntroduction());
        if (Objects.nonNull(dto.getStartTime())) {
            vo.setStartTime(dto.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        if (Objects.nonNull(dto.getEndTime())) {
            vo.setEndTime(dto.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        vo.setStatus(dto.getStatus());
        if (Objects.nonNull(dto.getConfig())) {
            RaceActivityConfigVO config = new RaceActivityConfigVO();
            config.setScoreOssId(dto.getConfig().getScoreOssId());
            if (StringUtils.hasText(dto.getConfig().getScoreOssId())) {
                config.setScoreOssUrl(ossManager.getOssFile(dto.getConfig().getScoreOssId()).map(OssDTO::getUrl).orElse(""));
            }
            vo.setConfig(config);
        }
        RaceLiveBroadcastDTO liveBroadcast = raceManager.getLatestLiveBroadcast(dto.getId());
        if (Objects.nonNull(liveBroadcast)) {

            FeedVO feedVO = feedV2Converter.convert(FeedContext.builder().id(liveBroadcast.getResourceId()).build());
            if (Objects.nonNull(feedVO) && Objects.equals(MediaResourceStatus.ENABLE.getCode(), feedVO.getMediaStatus())) {
                RaceLiveBroadcastVO liveBroadcastVO = new RaceLiveBroadcastVO();
                liveBroadcastVO.setId(liveBroadcast.getId());
                liveBroadcastVO.setResourceId(liveBroadcast.getResourceId());
                liveBroadcastVO.setTitle(liveBroadcast.getTitle());
                liveBroadcastVO.setMediaTitle(feedVO.getTitle());
                liveBroadcastVO.setMediaType(feedVO.getMediaType());
                liveBroadcastVO.setMediaUrls(feedVO.getMediaUrls());
                vo.setLiveBroadcast(liveBroadcastVO);
            }
        }
        return vo;
    }

    /**
     * RaceScheduleDTO转RaceScheduleVO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public RaceScheduleVO convert(RaceScheduleDTO dto, Long jid) {
        if (Objects.isNull(dto)) {
            return null;
        }

        RaceScheduleVO vo = new RaceScheduleVO();
        vo.setId(dto.getId());
        vo.setActivityId(dto.getActivityId());
        vo.setTitle(dto.getTitle());
        vo.setIntroduction(dto.getIntroduction());
        vo.setHomeTeamId(dto.getHomeTeamId());
        vo.setAwayTeamId(dto.getAwayTeamId());
        vo.setHomeTeamScore(dto.getHomeTeamScore());
        vo.setAwayTeamScore(dto.getAwayTeamScore());
        vo.setStatus(dto.getStatus());
        vo.setHomeTeamCheer(dto.getHomeTeamCheer());
        vo.setAwayTeamCheer(dto.getAwayTeamCheer());

        // 转换时间戳
        if (Objects.nonNull(dto.getStartTime())) {
            vo.setStartTime(dto.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        if (Objects.nonNull(dto.getEndTime())) {
            vo.setEndTime(dto.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }

        if (Objects.nonNull(dto.getHomeTeamId())) {
            RaceTeamDTO raceTeamDTO = raceManager.getTeamId(dto.getHomeTeamId());
            if (Objects.nonNull(raceTeamDTO)) {
                vo.setHomeTeamName(raceTeamDTO.getTitle());
                if (StringUtils.hasText(raceTeamDTO.getIcon())) {
                    vo.setHomeTeamIcon(ossManager.getOssFile(raceTeamDTO.getIcon()).map(OssDTO::getUrl).orElse(""));
                }
            }
        }

        if (Objects.nonNull(dto.getAwayTeamId())) {
            RaceTeamDTO raceTeamDTO = raceManager.getTeamId(dto.getAwayTeamId());
            if (Objects.nonNull(raceTeamDTO)) {
                vo.setAwayTeamName(raceTeamDTO.getTitle());
                if (StringUtils.hasText(raceTeamDTO.getIcon())) {
                    vo.setAwayTeamIcon(ossManager.getOssFile(raceTeamDTO.getIcon()).map(OssDTO::getUrl).orElse(""));
                }
            }
        }

        if (Objects.nonNull(jid)) {
            String key = String.format(RedisConstants.RACE_ACTIVITY_SCHEDULE_SUBSCRIBE_KEY, dto.getId());
            vo.setSubscribed(stringRedisTemplate.opsForSet().isMember(key, String.valueOf(jid)));
        }

        if (Objects.nonNull(dto.getResourceId())) {
            FeedVO feedVO = feedV2Converter.convert(FeedContext.builder().id(dto.getResourceId()).build());
            if (Objects.nonNull(feedVO) && Objects.equals(MediaResourceStatus.ENABLE.getCode(), feedVO.getMediaStatus())) {
                RaceScheduleVO.ResourceVO resourceVO = new RaceScheduleVO.ResourceVO();
                resourceVO.setId(feedVO.getId());
                resourceVO.setMediaType(feedVO.getMediaType());
                vo.setResourceVO(resourceVO);
            }
        }
        return vo;
    }

}
