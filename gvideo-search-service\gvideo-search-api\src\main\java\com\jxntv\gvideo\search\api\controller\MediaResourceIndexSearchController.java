package com.jxntv.gvideo.search.api.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.api.converter.MediaResourceIndexConverter;
import com.jxntv.gvideo.search.api.domain.MediaResourceIndex;
import com.jxntv.gvideo.search.api.service.MediaResourceIndexService;
import com.jxntv.gvideo.search.api.utils.PageUtils;
import com.jxntv.gvideo.search.client.MediaResourceIndexSearchClient;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexDTO;
import com.jxntv.gvideo.search.client.dto.MediaResourceIndexSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class MediaResourceIndexSearchController implements MediaResourceIndexSearchClient {

    @Resource
    private MediaResourceIndexService mediaResourceIndexService;
    @Resource
    private MediaResourceIndexConverter mediaResourceIndexConverter;

    @Override
    public Result<PageDTO<MediaResourceIndexDTO>> search(MediaResourceIndexSearchDTO searchDTO) {
        Page<MediaResourceIndex> page = mediaResourceIndexService.search(searchDTO);
        return Result.ok(PageUtils.pageOf(page, mediaResourceIndexConverter::convert));
    }


    @GetMapping("/api/media/resource/index/init")
    public Result<Boolean> init() {
        mediaResourceIndexService.init();
        return Result.ok(true);
    }


    @PostMapping("/api/media/resource/index")
    public Result<Boolean> index(@RequestBody List<Long> ids) {
        mediaResourceIndexService.batchIndexByIds(ids);
        return Result.ok(true);
    }


}
