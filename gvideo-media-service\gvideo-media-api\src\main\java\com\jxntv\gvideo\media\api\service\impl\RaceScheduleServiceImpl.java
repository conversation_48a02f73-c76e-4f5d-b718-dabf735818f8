package com.jxntv.gvideo.media.api.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.media.api.converter.RaceConverter;
import com.jxntv.gvideo.media.api.domain.entity.RaceActivity;
import com.jxntv.gvideo.media.api.domain.entity.RaceSchedule;
import com.jxntv.gvideo.media.api.repository.RaceScheduleMapper;
import com.jxntv.gvideo.media.api.service.RaceActivityService;
import com.jxntv.gvideo.media.api.service.RaceScheduleService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleSearchDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 比赛赛程服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RaceScheduleServiceImpl extends ServiceImpl<RaceScheduleMapper, RaceSchedule> implements RaceScheduleService {

    @Resource
    private RaceConverter raceConverter;

    @Resource
    private RaceActivityService raceActivityService;

    @Override
    public Boolean addSchedule(RaceScheduleDTO dto) {
        if (Objects.isNull(dto)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程信息不能为空");
        }
        if (Objects.isNull(dto.getActivityId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "活动ID不能为空");
        }
        if (Objects.isNull(dto.getHomeTeamId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "主队ID不能为空");
        }
        if (Objects.isNull(dto.getAwayTeamId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "客队ID不能为空");
        }
        if (Objects.equals(dto.getHomeTeamId(), dto.getAwayTeamId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "主队和客队不能是同一支队伍");
        }
        if (Objects.isNull(dto.getStartTime())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛开始时间不能为空");
        }
        if (Objects.isNull(dto.getEndTime())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛结束时间不能为空");
        }
        if (dto.getStartTime().isAfter(dto.getEndTime())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛开始时间不能晚于结束时间");
        }

        RaceSchedule entity = raceConverter.convert(dto);
        entity.setDelFlag(0);
        entity.setCreateDate(LocalDateTime.now());
        entity.setUpdateDate(LocalDateTime.now());

        // 设置比赛状态
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(entity.getStartTime())) {
            entity.setStatus(0); // 未开始
        } else if (now.isAfter(entity.getEndTime())) {
            entity.setStatus(2); // 已结束
        } else {
            entity.setStatus(1); // 进行中
        }
        return this.save(entity);
    }

    @Override
    public Boolean updateSchedule(RaceScheduleDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程ID不能为空");
        }
        if (Objects.isNull(dto.getActivityId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "活动ID不能为空");
        }
        if (Objects.isNull(dto.getHomeTeamId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "主队ID不能为空");
        }
        if (Objects.isNull(dto.getAwayTeamId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "客队ID不能为空");
        }
        if (Objects.equals(dto.getHomeTeamId(), dto.getAwayTeamId())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "主队和客队不能是同一支队伍");
        }
        if (Objects.isNull(dto.getStartTime())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛开始时间不能为空");
        }
        if (Objects.isNull(dto.getEndTime())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛结束时间不能为空");
        }
        if (dto.getStartTime().isAfter(dto.getEndTime())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "比赛开始时间不能晚于结束时间");
        }

        // 检查赛程是否存在
        RaceSchedule existingSchedule = this.getById(dto.getId());
        if (Objects.isNull(existingSchedule) || Objects.equals(1, existingSchedule.getDelFlag())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程不存在");
        }

        RaceSchedule entity = raceConverter.convert(dto);
        entity.setUpdateDate(LocalDateTime.now());

        // 更新比赛状态
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(entity.getStartTime())) {
            entity.setStatus(0); // 未开始
        } else if (now.isAfter(entity.getEndTime())) {
            entity.setStatus(2); // 已结束
        } else {
            entity.setStatus(1); // 进行中
        }

        return this.updateById(entity);
    }

    @Override
    public PageDTO<RaceScheduleDTO> page(RaceScheduleSearchDTO searchDTO) {
        LambdaQueryWrapper<RaceSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RaceSchedule::getDelFlag, 0);

        // 设置查询条件
        if (Objects.nonNull(searchDTO.getId())) {
            queryWrapper.eq(RaceSchedule::getId, searchDTO.getId());
        }
        if (Objects.nonNull(searchDTO.getActivityId())) {
            queryWrapper.eq(RaceSchedule::getActivityId, searchDTO.getActivityId());
        }
        if (Objects.nonNull(searchDTO.getHomeTeamId())) {
            queryWrapper.eq(RaceSchedule::getHomeTeamId, searchDTO.getHomeTeamId());
        }
        if (Objects.nonNull(searchDTO.getAwayTeamId())) {
            queryWrapper.eq(RaceSchedule::getAwayTeamId, searchDTO.getAwayTeamId());
        }
        if (Objects.nonNull(searchDTO.getStatus())) {
            queryWrapper.eq(RaceSchedule::getStatus, searchDTO.getStatus());
        }
        if (StringUtils.hasText(searchDTO.getTitle())) {
            queryWrapper.like(RaceSchedule::getTitle, searchDTO.getTitle());
        }
        if (Objects.nonNull(searchDTO.getResourceId())) {
            queryWrapper.eq(RaceSchedule::getResourceId, searchDTO.getResourceId());
        }
        if (StringUtils.hasText(searchDTO.getStartTime())) {
            queryWrapper.ge(RaceSchedule::getStartTime, searchDTO.getStartTime());
        }
        if (StringUtils.hasText(searchDTO.getEndTime())) {
            queryWrapper.le(RaceSchedule::getEndTime, searchDTO.getEndTime());
        }

        // 设置排序
        if (!CollectionUtils.isEmpty(searchDTO.getSorts())) {
            searchDTO.getSorts().forEach(sort -> {
                if ("startTime".equals(sort.getOrderBy())) {
                    if (sort.isAsc()) {
                        queryWrapper.orderByAsc(RaceSchedule::getStartTime);
                    } else {
                        queryWrapper.orderByDesc(RaceSchedule::getStartTime);
                    }
                } else if ("endTime".equals(sort.getOrderBy())) {
                    if (sort.isAsc()) {
                        queryWrapper.orderByAsc(RaceSchedule::getEndTime);
                    } else {
                        queryWrapper.orderByDesc(RaceSchedule::getEndTime);
                    }
                } else if ("status".equals(sort.getOrderBy())) {
                    if (sort.isAsc()) {
                        queryWrapper.orderByAsc(RaceSchedule::getStatus);
                    } else {
                        queryWrapper.orderByDesc(RaceSchedule::getStatus);
                    }
                } else if ("createDate".equals(sort.getOrderBy())) {
                    if (sort.isAsc()) {
                        queryWrapper.orderByAsc(RaceSchedule::getCreateDate);
                    } else {
                        queryWrapper.orderByDesc(RaceSchedule::getCreateDate);
                    }
                } else if ("id".equals(sort.getOrderBy())) {
                    if (sort.isAsc()) {
                        queryWrapper.orderByAsc(RaceSchedule::getId);
                    } else {
                        queryWrapper.orderByDesc(RaceSchedule::getId);
                    }
                }
            });
        } else {
            // 默认按开始时间升序
            queryWrapper.orderByAsc(RaceSchedule::getStartTime);
        }

        // 分页查询
        IPage<RaceSchedule> page = new Page<>(searchDTO.getCurrent(), searchDTO.getSize());
        page = this.page(page, queryWrapper);

        return PageUtils.pageOf(page, raceConverter::convert);
    }


    @Override
    public RaceScheduleDTO getScheduleById(Long id) {
        if (Objects.isNull(id)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程ID不能为空");
        }

        RaceSchedule raceSchedule = this.getById(id);
        if (Objects.isNull(raceSchedule) || Objects.equals(1, raceSchedule.getDelFlag())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程不存在");
        }

        // 更新赛程状态
        // this.updateScheduleStatus(raceSchedule);

        return raceConverter.convert(raceSchedule);
    }

    @Override
    public List<RaceScheduleDTO> getUserSubscribeList(Long activityId, String deviceId) {
        if (Objects.isNull(activityId)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "活动ID不能为空");
        }
        if (Objects.isNull(deviceId)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "设备ID不能为空");
        }

        List<RaceSchedule> schedules = this.baseMapper.selectUserSubscribeList(activityId, deviceId);
        return schedules.stream().map(raceConverter::convert).collect(Collectors.toList());
    }

    /**
     * 更新赛程状态
     *
     * @param raceSchedule 赛程实体
     */
    private void updateScheduleStatus(RaceSchedule raceSchedule) {
        LocalDateTime now = LocalDateTime.now();
        Integer oldStatus = raceSchedule.getStatus();
        Integer newStatus = oldStatus;

        // 判断赛程状态
        if (now.isBefore(raceSchedule.getStartTime())) {
            newStatus = 0; // 未开始
        } else if (now.isAfter(raceSchedule.getEndTime())) {
            newStatus = 2; // 已结束
        } else {
            newStatus = 1; // 进行中
        }

        // 如果状态有变化，则更新
        if (!Objects.equals(oldStatus, newStatus)) {
            raceSchedule.setStatus(newStatus);
            raceSchedule.setUpdateDate(LocalDateTime.now());
            this.updateById(raceSchedule);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSchedule(Long id) {
        if (Objects.isNull(id)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程ID不能为空");
        }

        // 检查赛程是否存在
        RaceSchedule raceSchedule = this.getById(id);
        if (Objects.isNull(raceSchedule) || Objects.equals(1, raceSchedule.getDelFlag())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "赛程不存在");
        }

        // 逻辑删除赛程
        raceSchedule.setDelFlag(1);
        raceSchedule.setUpdateDate(LocalDateTime.now());
        return this.updateById(raceSchedule);
    }

    @Override
    public Boolean initScheduleCheer(Long activityId) {
        RaceActivity raceActivity = raceActivityService.getById(activityId);
        if (Objects.isNull(raceActivity) || Objects.isNull(raceActivity.getConfig())) {
            return false;
        }

        Integer minInitialCheer = Objects.nonNull(raceActivity.getConfig().getMinInitialCheer()) && raceActivity.getConfig().getMinInitialCheer() > 0 ? raceActivity.getConfig().getMinInitialCheer() : 3000;
        Integer maxInitialCheer = Objects.nonNull(raceActivity.getConfig().getMaxInitialCheer()) && raceActivity.getConfig().getMaxInitialCheer() > 0 ? raceActivity.getConfig().getMaxInitialCheer() : 4000;

        LambdaQueryWrapper<RaceSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RaceSchedule::getActivityId, activityId);
        queryWrapper.eq(RaceSchedule::getDelFlag, 0);
        queryWrapper.isNull(RaceSchedule::getHomeTeamCheer).or().eq(RaceSchedule::getHomeTeamCheer, 0);
        queryWrapper.isNull(RaceSchedule::getAwayTeamCheer).or().eq(RaceSchedule::getAwayTeamCheer, 0);

        List<RaceSchedule> raceSchedules = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(raceSchedules)) {
            return false;
        }

        for (RaceSchedule raceSchedule : raceSchedules) {
            Integer randomCheer = RandomUtils.nextInt(minInitialCheer, maxInitialCheer);
            // 取randomCheer的1/3 到 2/3取一个数
            Integer homeTeamCheer = RandomUtils.nextInt(randomCheer / 3, randomCheer * 2 / 3);
            Integer awayTeamCheer = randomCheer - homeTeamCheer;
            raceSchedule.setHomeTeamCheer(homeTeamCheer);
            raceSchedule.setAwayTeamCheer(awayTeamCheer);
            raceSchedule.setUpdateDate(LocalDateTime.now());
        }

        return this.updateBatchById(raceSchedules);
    }
}
