package com.jxntv.gvideo.media.client.dto.redactivity;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class RedActivityQueryParam implements Serializable {

    private Integer page;

    private Integer pageSize;

    private String type;

    private String keyword;

    private String mobile;

    /**
     * 评委 id
     */
    private Long judgeId;

    /**
     * 终审状态，1：未终审，2：已终审
     */
    private Integer finalState;

    /**
     * 评委手机号
     */
    private String judgePhone;

    /**
     * 打分状态：1：未打分，2：已打分
     */
    private Integer state;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品类型 list
     */
    private List<String> productTypeList;

    /**
     * 外部传入的 产品类型 list
     */
    private List<String> outProductTypeList;


    /**
     * 作品id list
     */
    private List<JudgeTrackIdConfig> productIdList;
}
