package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.media.api.domain.entity.RaceSchedule;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleDTO;
import com.jxntv.gvideo.media.client.dto.race.RaceScheduleSearchDTO;

import java.util.List;

/**
 * 比赛赛程服务接口
 *
 * <AUTHOR>
 */
public interface RaceScheduleService extends IService<RaceSchedule> {

    /**
     * 新增赛程信息
     *
     * @param dto 赛程信息
     * @return 处理结果
     */
    Boolean addSchedule(RaceScheduleDTO dto);

    /**
     * 修改赛程信息
     *
     * @param dto 赛程信息
     * @return 处理结果
     */
    Boolean updateSchedule(RaceScheduleDTO dto);

    /**
     * 分页查询赛程列表
     *
     * @param searchDTO 查询条件
     * @return 赛程分页数据
     */
    PageDTO<RaceScheduleDTO> page(RaceScheduleSearchDTO searchDTO);


    /**
     * 赛程详情
     *
     * @param id 赛程ID
     * @return 赛程详情信息
     */
    RaceScheduleDTO getScheduleById(Long id);

    /**
     * 查询用户预约的赛程列表
     *
     * @param activityId 活动ID
     * @param deviceId 设备ID
     * @return 预约的赛程列表
     */
    List<RaceScheduleDTO> getUserSubscribeList(Long activityId, String deviceId);

    /**
     * 删除赛程
     *
     * @param id 赛程ID
     * @return 删除结果
     */
    Boolean deleteSchedule(Long id);



    /**
     * 初始化赛程助威数
     *
     * @param activityId 活动ID
     * @return 初始化结果
     */
    Boolean initScheduleCheer(Long activityId);
}
