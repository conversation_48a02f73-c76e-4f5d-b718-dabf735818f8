package com.jxntv.gvideo.search.api.domain;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.InnerField;
import org.springframework.data.elasticsearch.annotations.MultiField;

import com.jxntv.gvideo.search.api.common.IndexEntity;

import lombok.Data;

@Data
@Document(indexName = "media_resource_index_20250715")
public class MediaResourceIndex implements IndexEntity, Serializable {
    @Id
    private String id;

    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word"), otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String title;

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word")
    private String description;

    @Field(type = FieldType.Keyword)
    private String contentId; // 内容ID字段

    @Field(type = FieldType.Keyword)
    private String videoUrl; // 视频url

    @Field(type = FieldType.Keyword)
    private String thumb; // 视频封面图url

    @Field(type = FieldType.Integer)
    private Integer status; // 状态
  
    @Field(type = FieldType.Long)
    private Long createTime; // 创建时间戳

    @Field(type = FieldType.Nested)
    private List<Label> labels; // 标签嵌套结构

    @Field(type = FieldType.Nested)
    private List<Category> categories; // 分类嵌套结构

    @Data
    public static class Label {
        @Field(type = FieldType.Integer)
        private Integer type;// 标签类型

        @Field(type = FieldType.Long)
        private Long labelId; // 标签ID

        @Field(type = FieldType.Keyword)
        private String labelName; // 标签名称

    }

    @Data
    public static class Category {

        @Field(type = FieldType.Long)
        private Long categoryId; // 分类ID

        @Field(type = FieldType.Keyword)
        private String categoryName; // 分类名称

    }

}
