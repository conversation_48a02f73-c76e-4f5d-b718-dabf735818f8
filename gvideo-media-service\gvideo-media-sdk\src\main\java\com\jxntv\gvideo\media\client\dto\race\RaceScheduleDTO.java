package com.jxntv.gvideo.media.client.dto.race;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 比赛赛程DTO类
 *
 * <AUTHOR>
 */
@Data
public class RaceScheduleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 赛程id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 比赛标题（轮次）
     */
    private String title;

    /**
     * 比赛活动简介
     */
    private String introduction;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 主队id
     */
    private Long homeTeamId;

    /**
     * 客队id
     */
    private Long awayTeamId;

    /**
     * 主队得分
     */
    private Integer homeTeamScore;

    /**
     * 客队得分
     */
    private Integer awayTeamScore;

    /**
     * 主队助威数（初始值）
     */
    private Integer homeTeamCheer;

    /**
     * 客队助威数（初始值）
     */
    private Integer awayTeamCheer;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 预约推送标题
     */
    private String pushTitle;

    /**
     * 预约推送内容
     */
    private String pushContent;

    /**
     * 状态：0-未开始、1-进行中、2-已结束
     */
    private Integer status;

    /**
     * 删除标志 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}