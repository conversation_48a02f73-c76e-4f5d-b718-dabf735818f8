package com.jxntv.gvideo.media.client.dto.redactivity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 报名DTO
 */
@Data
public class RedActivityActiveDTO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    @ExcelProperty(value = "编号", index = 0)
    private Integer sort;

    @ExcelProperty(value = "终审编号", index = 0)
    private Integer finalSort;

    /**
     * 报名类型：机构、个人
     */
    @ExcelProperty(value = "报名类型", index = 1)
    private String type;


    /**
     * 作品名称
     */
    @ExcelProperty(value = "作品名称", index = 2)
    private String productName;

    /**
     * 作品类型
     */
    @ExcelProperty(value = "作品类型", index = 3)
    private String productType;

    /**
     * 作品简介
     */
    @ExcelProperty(value = "作品简介", index = 4)
    private String productIntro;

    /**
     * 创作日期
     */
    @ExcelIgnore
    private String productDate;

    /**
     * 创建人员，多个用逗号分割
     */
    @ExcelProperty(value = "创建人员", index = 5)
    private String creators;

    /**
     * 第一主创人工作单位
     */
    private String firstCreatorCompany;;

    /**
     * 第一主创人省份
     */
    private String firstCreatorProvince;


    /**
     * 第一主创人职业
     */
    private String firstCreatorOccupation;

    /**
     * 第一主创人民族
     */
    private String firstCreatorEthnic;

    /**
     * 第一主创人性别：0-男，1-女
     */
    private Integer firstCreatorGender = 0;

    /**
     * 第一主创人年龄
     */
    private Integer firstCreatorAge;

    /**
     * 创办单位
     */
    @ExcelProperty(value = "创办单位", index = 6)
    private String founders;

    /**
     * 发布状态
     */
    @ExcelIgnore
    private String releaseState;

    /**
     * 发布日期
     */
    @ExcelIgnore
    private String releaseDate;

    /**
     * 报名文档url
     */
    @ExcelProperty(value = "报名文档", index = 9)
    private String docUrl;

    /**
     * 报名视频url
     */
    @ExcelProperty(value = "作品视频", index = 10)
    private String videoUrl;

    /**
     * 转码后的标清视频
     */
    @ExcelIgnore
    private String bqVideoUrl;

    /**
     * 手机号
     */
    @ExcelIgnore
    private String mobile;

    /**
     * 手机验证码
     */
    @ExcelIgnore
    private String mobileCode;

    /**
     * 打分状态
     */
    @ExcelIgnore
    private Integer state;

    @ExcelProperty(value = "打分状态", index = 7)
    private String stateStr;

    @ExcelProperty(value = "最终得分", index = 8)
    private BigDecimal score;

    /**
     * 创建时间qq
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private List<Integer> judgeScores;
}
